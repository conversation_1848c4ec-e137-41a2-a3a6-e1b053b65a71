[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-gdb"
version = "0.1.0"
description = "AI自动化GDB调试工具，借助大模型+AI Agent能力，实现完全自动化的GDB单步调试能力"
authors = [
    {name = "AI-GDB Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "loguru>=0.7.0",
    "openai>=1.0.0",
    "langchain>=0.1.0",
    "langchain-openai>=0.0.5",
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    "faiss-cpu>=1.7.0",
    "tree-sitter>=0.20.0",
    "tree-sitter-c>=0.20.0",
    "tree-sitter-cpp>=0.20.0",
    "clang>=16.0.0",
    "fastapi>=0.100.0",
    "uvicorn>=0.23.0",
    "websockets>=11.0.0",
    "jinja2>=3.1.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[project.scripts]
ai-gdb = "ai_gdb.cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=ai_gdb --cov-report=html --cov-report=term-missing"
