# 项目描述
AI自动化GDB调试工具，借助大模型+AI Agent能力，实现完全自动化的GDB单步调试能力，解决C/c++项目代码的故障。
## 核心模块
1. **项目代码知识库**：项目代码知识库，检索相关代码。
2. **GDB控制层**：Python `gdb` 模块封装
3. **AI Agent决策引擎**：LLM + 工具调用
4. **修复执行器**：代码热修补/make编译

## 工作流
1.对项目代码创建知识库
2.根据用户提供的故障信息，分析可能存在的问题点，并通过GDB控制层加断点
3.启动执行文件
4.运行会复现故障的测试脚本
5.故障复现或到断点时，将通过gdb捕获的上下文（堆栈帧、内存变量值、相关代码等）发送到LLM
6.如果需要单步调试，则需要调用GDB控制层，并获取GDB的输出：单步调试可以反复多次
7.如果单步调试过程中复现故障，则：
    7.1 让LLM判断，是否已经100%确定故障的原因，并给出具体的分析，终止工具运行
    7.2 如果LLM不能完全确定故障原因，则可能需要工具调用，比如增加新的断点、动态插桩,并转到第3步，重新运行测试脚本、验证
8.如果调试过程中出现10次故障（可配置）并且还未100%确定故障原因，则给出分析报告，并提示人工检查、手动修复

## 项目代码库及知识检索
使用LLM+向量数据库进行代码解析，生成知识图谱，做为AI agent工具让大模型调用:
1. 静态解析：解析C/C++代码，生成知识图谱
2. 使用向量数据库进行索引，分析可能的故障原因，确定在哪个地方加断点
3. 获取代码文件全文
4. 如果代码有修改，可配置重建代码知识库

## GDB控制层
用Python的gdb模块实现以下功能：
1. 自动附加进程/加载可执行文件
2. 动态设置断点（函数/行号/异常点）
3. 捕获以下关键信息：
   - 堆栈帧（backtrace）
   - 寄存器值（info registers）
   - 内存变量值
   - 内存泄露检测（valgrind集成）
   - 信号捕获（SIGSEGV等）
   - 执行文件的控制台输出中的错误信息
   - 执行日志中的错误信息
4. 支持非交互式命令执行
5. 实时流式解析GDB输出
6. 感知程序执行状态：执行到断点、捕获到错误信号、有错误输出等

## AI Agent决策引擎
设计一个基于LLM的调试Agent：
1. 输入层：结构化调试上下文：
   {
     "stack_trace": [...],
     "variables": {name:value},
     "breakpoint": "file.c:line",
     "error": "SIGSEGV in malloc()"
     "log": [错误日志],
     "code": [相关代码],
   }
2. 工具：
  - GDB控制层工具：执行GDB命令，如运行文件、捕获内存变量、堆栈、变量、错误信息、单步调试等
  - 代码检索：检索代码知识库，获取相关代码片段
  - 执行测试脚本
  - 获取日志文件中的错误信息
3. 决策流程：（参考工作流部分描述）
   - LLM调用：当发生故障或断点时，将调试上下文发送给LLM，并获取建议
   - 系统提示词：通过系统提示词，让大模型准确的分析故障、执行任务
   - 工具调用：解析LLM返回的工具调用，并执行
   - 记忆：1.在内存记录单步调试的步骤，并发送给LLM 2.在调试过程中复现故障，并重新启动程序时，让LLM总结本次调试过程，并重置记忆
   
3. 输出层：执行安全命令白名单中的操作

## 修复执行器
预留接口，暂不实现。

## 其他要求
1. 配置管理：可通过.env文件配置代码库目录、GDB路径、执行文件路径、测试脚本路径、大模型URL/APIKEY/模型名称、LLM提示词等参数
2. 可视化展现整个自动化GDB过程（如文件的单步调试、变量捕获等、gdb命令）
3. 使用python实现