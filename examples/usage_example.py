#!/usr/bin/env python3
"""
AI-GDB 使用示例

这个脚本演示了如何使用AI-GDB进行自动化调试
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ai_gdb.config import Config
from ai_gdb.knowledge_base import KnowledgeBase
from ai_gdb.gdb_controller import GDBController
from ai_gdb.ai_agent import AIAgent


async def main():
    """主函数"""
    print("=== AI-GDB 使用示例 ===\n")
    
    # 1. 配置设置
    print("1. 设置配置...")
    
    # 获取示例项目路径
    sample_project = Path(__file__).parent / "sample_project"
    
    # 创建配置
    config = Config(
        project_code_dir=sample_project,
        llm_api_key="your_openai_api_key_here",  # 请替换为实际的API密钥
        llm_provider="openai",
        target_executable=sample_project / "sample_program",
        vector_db_path=sample_project / "vector_db",
        log_file_path=sample_project / "ai_gdb.log",
        log_level="INFO"
    )
    
    print(f"项目目录: {config.project_code_dir}")
    print(f"目标程序: {config.target_executable}")
    
    # 2. 构建知识库
    print("\n2. 构建代码知识库...")
    
    knowledge_base = KnowledgeBase(config)
    
    try:
        await knowledge_base.build_knowledge_base()
        print("知识库构建完成")
        
        # 显示统计信息
        stats = knowledge_base.get_statistics()
        print(f"代码块数量: {stats['total_chunks']}")
        print(f"符号数量: {stats['total_symbols']}")
        print(f"文件数量: {stats['file_count']}")
        
    except Exception as e:
        print(f"知识库构建失败: {e}")
        return
    
    # 3. 测试代码搜索
    print("\n3. 测试代码搜索...")
    
    search_results = knowledge_base.search_code("divide function", top_k=3)
    print(f"搜索结果数量: {len(search_results)}")
    
    for i, result in enumerate(search_results):
        print(f"结果 {i+1}:")
        print(f"  文件: {result.chunk.file_path}")
        print(f"  相似度: {result.score:.3f}")
        print(f"  符号: {[s.name for s in result.chunk.symbols]}")
    
    # 4. 创建AI Agent
    print("\n4. 创建AI调试代理...")
    
    gdb_controller = GDBController(config)
    ai_agent = AIAgent(config, knowledge_base, gdb_controller)
    
    print("AI Agent创建完成")
    
    # 5. 模拟调试会话（如果有有效的API密钥）
    if config.llm_api_key != "your_openai_api_key_here":
        print("\n5. 开始调试会话...")
        
        fault_description = "程序在执行除法运算时发生段错误"
        
        try:
            result = await ai_agent.start_debug_session(fault_description)
            
            print("调试会话结果:")
            print(f"成功: {result.get('success', False)}")
            print(f"轮次: {result.get('rounds', 0)}")
            
            if result.get('success'):
                print(f"解决方案: {result.get('solution', '无')}")
            else:
                print(f"错误: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"调试会话失败: {e}")
    else:
        print("\n5. 跳过调试会话（需要有效的API密钥）")
        print("请在配置中设置有效的LLM API密钥以启用完整功能")
    
    # 6. 显示记忆统计
    print("\n6. 记忆管理统计...")
    
    patterns = ai_agent.memory_manager.get_historical_patterns()
    print(f"历史会话数量: {len(ai_agent.memory_manager.sessions)}")
    print(f"成功率: {patterns.get('success_rate', 0):.2%}")
    
    print("\n=== 示例完成 ===")


def setup_example_project():
    """设置示例项目"""
    sample_project = Path(__file__).parent / "sample_project"
    
    print("设置示例项目...")
    
    # 检查是否存在Makefile
    makefile = sample_project / "Makefile"
    if makefile.exists():
        print("编译示例程序...")
        
        import subprocess
        try:
            # 编译程序
            result = subprocess.run(
                ["make", "debug"],
                cwd=sample_project,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("编译成功")
                return True
            else:
                print(f"编译失败: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("未找到make命令，请安装build-essential")
            return False
    else:
        print("未找到Makefile")
        return False


if __name__ == "__main__":
    print("AI-GDB 使用示例\n")
    
    # 设置示例项目
    if setup_example_project():
        # 运行主程序
        asyncio.run(main())
    else:
        print("示例项目设置失败，请检查依赖")
        
        print("\n手动设置步骤:")
        print("1. cd examples/sample_project")
        print("2. make debug")
        print("3. 设置有效的LLM API密钥")
        print("4. 重新运行此脚本")
