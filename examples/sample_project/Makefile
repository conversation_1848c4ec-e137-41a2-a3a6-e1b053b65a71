# Makefile for AI-GDB sample project

CC = gcc
CFLAGS = -g -Wall -Wextra -std=c99
TARGET = sample_program
SOURCES = main.c

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(SOURCES)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCES)

# 调试版本（包含更多调试信息）
debug: CFLAGS += -DDEBUG -O0
debug: $(TARGET)

# 清理
clean:
	rm -f $(TARGET) *.o

# 运行程序
run: $(TARGET)
	./$(TARGET)

# 使用GDB运行
gdb: $(TARGET)
	gdb ./$(TARGET)

# 使用Valgrind检查内存
valgrind: $(TARGET)
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET)

# 安装依赖（Ubuntu/Debian）
install-deps:
	sudo apt-get update
	sudo apt-get install -y gcc gdb valgrind

.PHONY: all debug clean run gdb valgrind install-deps
