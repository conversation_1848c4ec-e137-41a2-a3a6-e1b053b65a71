/*
 * 示例C程序 - 包含常见的调试场景
 * 这个程序故意包含一些bug用于演示AI-GDB的调试能力
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 全局变量
int global_counter = 0;

// 函数声明
int divide_numbers(int a, int b);
void process_array(int *arr, int size);
char* create_string(const char* input);
void memory_leak_example();

int main() {
    printf("=== AI-GDB 示例程序 ===\n");
    
    // 场景1: 除零错误
    printf("\n1. 测试除法运算:\n");
    int x = 10;
    int y = 0;  // 这里会导致除零错误
    
    int result = divide_numbers(x, y);
    printf("10 / 0 = %d\n", result);
    
    // 场景2: 数组越界
    printf("\n2. 测试数组处理:\n");
    int numbers[5] = {1, 2, 3, 4, 5};
    process_array(numbers, 10);  // 传入错误的大小
    
    // 场景3: 内存问题
    printf("\n3. 测试字符串处理:\n");
    char* str = create_string("Hello World");
    printf("创建的字符串: %s\n", str);
    // 忘记释放内存
    
    // 场景4: 内存泄露
    printf("\n4. 内存泄露示例:\n");
    memory_leak_example();
    
    printf("\n程序结束\n");
    return 0;
}

/**
 * 除法函数 - 包含除零错误
 */
int divide_numbers(int a, int b) {
    // 缺少除零检查
    return a / b;  // 当b为0时会出错
}

/**
 * 数组处理函数 - 包含数组越界错误
 */
void process_array(int *arr, int size) {
    printf("处理数组，大小: %d\n", size);
    
    for (int i = 0; i < size; i++) {
        // 当size大于实际数组大小时会越界
        printf("arr[%d] = %d\n", i, arr[i]);
        arr[i] *= 2;  // 修改数组元素
        global_counter++;
    }
    
    printf("全局计数器: %d\n", global_counter);
}

/**
 * 字符串创建函数 - 包含内存管理问题
 */
char* create_string(const char* input) {
    if (input == NULL) {
        return NULL;
    }
    
    int len = strlen(input);
    char* result = malloc(len + 1);
    
    if (result == NULL) {
        printf("内存分配失败!\n");
        return NULL;
    }
    
    // 使用不安全的字符串复制
    strcpy(result, input);
    
    return result;  // 调用者需要释放内存
}

/**
 * 内存泄露示例函数
 */
void memory_leak_example() {
    for (int i = 0; i < 5; i++) {
        // 分配内存但不释放
        char* buffer = malloc(100);
        if (buffer) {
            sprintf(buffer, "Buffer %d", i);
            printf("分配缓冲区: %s\n", buffer);
            // 忘记调用 free(buffer);
        }
    }
}

/**
 * 递归函数 - 可能导致栈溢出
 */
int factorial(int n) {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);  // 没有检查栈深度
}

/**
 * 指针操作函数 - 包含空指针解引用
 */
void pointer_operations() {
    int* ptr = NULL;
    
    // 空指针解引用
    *ptr = 42;  // 这里会导致段错误
    
    printf("指针值: %d\n", *ptr);
}
