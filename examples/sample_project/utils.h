/*
 * 工具函数头文件
 */

#ifndef UTILS_H
#define UTILS_H

// 数学工具函数
int divide_numbers(int a, int b);
int factorial(int n);

// 数组工具函数
void process_array(int *arr, int size);
int find_max(int *arr, int size);
void sort_array(int *arr, int size);

// 字符串工具函数
char* create_string(const char* input);
int string_length(const char* str);
char* string_concat(const char* str1, const char* str2);

// 内存管理函数
void memory_leak_example();
void* safe_malloc(size_t size);
void safe_free(void** ptr);

// 指针操作函数
void pointer_operations();
int* create_int_array(int size);
void print_int_array(int* arr, int size);

// 调试辅助函数
void print_debug_info(const char* function_name, int line_number);
void log_error(const char* error_message);

#endif // UTILS_H
