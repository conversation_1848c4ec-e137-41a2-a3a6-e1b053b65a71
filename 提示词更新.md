# 优化1
1 .env文件增加TARGET_ARGS参数，用于指定中被调试的程序运行时参数
2 create_system_prompt方法的系统提示词增加当前系统的可用工具，让大模型可以决策选择哪些工具
3 大模型辅助gdb的第一阶段，需要让大模型了解当前项目结构，可以根据错误信息和代码知识库搜索工具，大致判断一个可能的错误，并加断点开始调试，这个过程可能持续多次调用，直到agent了解代码全貌。（通过系统提示词+工具，让大模型分析）
4 _debug_loop的调式轮次并不是10轮大模型API调用， 当gdb调试复现到错误才算一轮次debug调试，如果复现错误并准确判断了错误原因或者10轮次复现错误都没有找到错误原因，则结束调试。
5 每个轮次的gdb调试，都是由agent自动完成，模拟程序员调试的步骤：根据代码分析可能的原因，并在代码处加几个断点，运行测试程序，在断点处检查变量值、内存堆栈，判断是否是错误原因，否则可选择单步调试到下一行代码或继续执行代码等步骤。（通过系统提示词+工具，让大模型分析）

# 调试轮次
1.每个调试轮次都是重启新的服务进程TARGET_EXECUTABLE。如果配置了TEST_SCRIPT_PATH,则在服务进程启动后执行测试脚本。
2.服务进程运行终止（崩溃错误或正常退出）或测试脚本执行完毕，做为一个调试轮次。如果测试脚本执行完毕，可结束服务进程。
3."gdb_session:_read_output:185 - GDB命令执行超时 "，超时时间在.env中配置
4.解决前端问题： 调试输出区域，只输出了[15:00:08] undefined

# 问题
1.WEB UI点击知识库，内容是空的
2.调试历史，查看详情，后端错误："GET /api/memory/sessions/88f01319 HTTP/1.1" 404 Not Found
3.输出"测试脚本"执行的控制台输出
4.后端服务已经：stop_debug_session:386 - GDB调试会话已停止，WEB ui的调试按钮依然是“调试中”
# 优化2
1..env增加CODE_FILTER参数，用于指定构建知识库时被过滤的代码目录或文件，多个目录或文件用英文逗号隔开，支持.gitignore语法

# 问题
加载已经构建完毕的向量知识库后，self.knowledge_base.find_symbol("main")、self.knowledge_base.get_all_files()返回值都是空的

# 知识库构建问题
_parse_with_tree_sitter方法解析代码结构，没有正确提取符号和代码块。以代码/ddrive/github/gdb-test/src/main.cpp为例，提取的CodeSymbol对象如下：
CodeSymbol(name='atic std:', type='variable', file_path='/ddrive/github/gdb-test/src/main.cpp', line_number=13, column=0, signature=None, docstring=None, parent=None, children=[])
CodeSymbol(name='gnal) {\n    L', type='function', file_path='/ddrive/github/gdb-test/src/main.cpp', line_number=18, column=0, signature='nt signal)', docstring=None, parent=None, children=[])
CodeSymbol(name='ignalHandler);\n    ', type='function', file_path='/ddrive/github/gdb-test/src/main.cpp', line_number=31, column=0, signature='NT, signalHandler);\n    signal(SIGTERM, signalHandler);\n    signal(SIGPIPE, SIG_IGN); // 忽略SIGPIPE信号\n}\n\nvoid printBanner()', docstring=None, parent=None, children=[])等符号，没有提取到正确的方法名。
解决此问题，并用/ddrive/github/gdb-test/src/main.cpp代码测试解析的正确性。
break main.cpp:48

# 断点设置问题
在当前项目运行：python -m src.cli debug --fault-description "并发性能测试中，发生段错误，系统crash"， 执行到set_breakpoint方法，设置断点命令'break main.cpp:48'，返回的output为：'(gdb)\n&"handle SIGSEGV stop print\\n"\n~"Signal        Stop\\tPrint\\tPass to program\\tDescription\\n"\n~"SIGSEGV       Yes\\tYes\\tYes\\t\\tSegmentation fault\\n"\n^done' ，
导致_parse_breakpoint_id方法解析失败：设置断点失败.
解决此问题，运行： python -m src.cli debug --fault-description "并发性能测试中，发生段错误，系统crash" ,检查问题是否已经解决

# 解析问题
调用栈解析不成功，backtrace命令的控制台输出如下：&"backtrace\\n"\n~"#0  kvdb::ConfigManager::loadFromFile (this=0x5555555f8620 <kvdb::ConfigManager::getInstance()::instance>, config_file=\\"/ddrive/github/gdb-test/config/kvdb.conf\\") at /ddrive/github/gdb-test/src/config/config_manager.cpp:30\\n"\n~"#1  0x00005555555623d0 in kvdb::ConfigManager::loadFromCommandLine (this=0x5555555f8620 <kvdb::ConfigManager::getInstance()::instance>, argc=3, argv=0x7fffffffd898) at /ddrive/github/gdb-test/src/config/config_manager.cpp:86\\n"\n~"#2  0x000055555555db7f in main (argc=3, argv=0x7fffffffd898) at /ddrive/github/gdb-test/src/main.cpp:71\\n"\n^done\n(gdb) \n ，优化此方法对backtrace命令的解析。