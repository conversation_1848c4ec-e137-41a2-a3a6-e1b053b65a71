# AI-GDB 自动化调试工具

借助大模型+AI Agent能力，实现完全自动化的GDB单步调试能力，解决C/C++项目代码的故障。

## 🚀 功能特性

### 🆕 最新改进 (v2.0)
- **工具感知AI**: LLM现在了解所有可用调试工具，能够智能选择最佳工具
- **项目结构分析**: 第一阶段深度分析项目结构，理解代码关系和潜在问题
- **真实调试轮次**: 重构调试逻辑，每轮次都要复现错误才算有效调试
- **完整自动化流程**: 6步完整调试流程，模拟程序员真实调试步骤
- **程序参数支持**: 支持为被调试程序配置运行时参数

### 核心功能
- **智能代码分析**: 基于大语言模型的智能代码分析，自动识别潜在问题
- **自动断点设置**: 基于项目分析智能设置断点位置，自动捕获关键执行点
- **单步调试**: 支持自动化的单步调试，包括step over、step into、step out
- **状态监控**: 实时监控程序状态、变量变化、内存使用和调用栈
- **代码知识库**: 构建项目代码的向量数据库，支持语义搜索和上下文分析
- **记忆管理**: 记录调试过程，学习调试模式，提高调试效率
- **Web界面**: 提供直观的Web界面，实时展示调试过程
- **多LLM支持**: 支持OpenAI、Anthropic等多种大语言模型

## 📋 系统要求

- Python 3.9+
- GDB (GNU Debugger)
- C/C++ 编译器 (gcc/clang)
- 大语言模型API密钥 (OpenAI/Anthropic)

## 🛠️ 安装

### 1. 克隆项目

```bash
git clone https://github.com/your-repo/ai-gdb.git
cd ai-gdb
```

### 2. 安装依赖

```bash
# 使用pip安装
pip install -r requirements.txt

# 或使用开发模式安装
pip install -e .
```

### 3. 安装系统依赖

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y gcc gdb valgrind

# CentOS/RHEL
sudo yum install -y gcc gdb valgrind

# macOS
brew install gcc gdb
```

## ⚙️ 配置

### 1. 创建配置文件

复制示例配置文件并修改：

```bash
cp .env.example .env
```

### 2. 编辑配置文件

```bash
# 项目配置
PROJECT_CODE_DIR=/path/to/your/c_cpp_project
GDB_PATH=/usr/bin/gdb
TARGET_EXECUTABLE=/path/to/your/executable
TARGET_ARGS=--input data.txt --verbose  # 新增：程序运行参数

# LLM配置
LLM_PROVIDER=openai
LLM_API_KEY=your_api_key_here
LLM_MODEL_NAME=gpt-4-turbo-preview

# 向量数据库配置
VECTOR_DB_TYPE=chroma
VECTOR_DB_PATH=./data/vector_db

# 调试配置
MAX_DEBUG_ROUNDS=10  # 最大调试轮次

# 其他配置...
```

## 🚀 快速开始

### 1. 构建代码知识库

```bash
ai-gdb build-kb --config .env

python -m src.cli build-kb
```

### 2. 开始自动化调试

```bash
ai-gdb debug --config .env --fault-description "程序在malloc时发生段错误"

python -m src.cli debug --fault-description "并发性能测试中，发生段错误，系统crash"
```

### 3. 启动Web界面

```bash
ai-gdb web --config .env
python -m src.cli web
```

然后在浏览器中访问 `http://localhost:8000`

## AI Agent决策引擎
设计一个基于LLM的调试Agent：
1. 输入层：结构化调试上下文：
   {
     "stack_trace": [...],
     "variables": {name:value},
     "breakpoint": "file.c:line",
     "error": "SIGSEGV in malloc()"
     "log": [错误日志],
     "code": [相关代码],
   }
2. 工具：
  - GDB控制层工具：执行GDB命令，如运行文件、捕获内存变量、堆栈、变量、错误信息、单步调试等
  - 代码检索：检索代码知识库，获取相关代码片段
  - 执行测试脚本
  - 获取日志文件中的错误信息
3. 决策流程：（参考工作流部分描述）
   - LLM调用：当发生故障或断点时，将调试上下文发送给LLM，并获取建议
   - 系统提示词：通过系统提示词，让大模型准确的分析故障、执行任务
   - 工具调用：解析LLM返回的工具调用，并执行
   - 记忆：1.在内存记录单步调试的步骤，并发送给LLM 2.在调试过程中复现故障，并重新启动程序时，让LLM总结本次调试过程，并重置记忆
   
3. 输出层：执行安全命令白名单中的操作

## 修复执行器
预留接口，暂不实现。

## 其他要求
1. 配置管理：可通过.env文件配置代码库目录、GDB路径、执行文件路径、测试脚本路径、大模型URL/APIKEY/模型名称、LLM提示词等参数
2. 可视化展现整个自动化GDB过程（如文件的单步调试、变量捕获等、gdb命令）
3. 使用python实现


