#!/usr/bin/env python3
"""
启动Web界面的简单脚本
"""

import sys
import asyncio
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from config.config import Config
from knowledge_base.knowledge_base import KnowledgeBase
from gdb_controller.gdb_controller import GDBController
from ai_agent.ai_agent import AIAgent
from web_ui.web_ui import WebUI


async def main():
    """主函数"""
    try:
        # 加载配置
        config = Config.from_env()
        print(f"配置加载完成，项目目录: {config.project_code_dir}")
        
        # 初始化知识库
        knowledge_base = KnowledgeBase(config)
        print("知识库初始化完成")
        
        # 初始化GDB控制器
        gdb_controller = GDBController(config)
        print("GDB控制器初始化完成")
        
        # 初始化AI Agent
        ai_agent = AIAgent(config, knowledge_base, gdb_controller)
        print("AI Agent初始化完成")
        
        # 启动Web界面
        web_ui = WebUI(config, ai_agent)
        print(f"启动Web界面: http://{config.web_host}:{config.web_port}")
        
        await web_ui.start()
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
