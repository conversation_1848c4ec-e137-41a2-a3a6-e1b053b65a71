# AI-GDB 自动化调试工具依赖包

# 核心依赖
python-dotenv>=1.0.0
pydantic>=2.0.0
loguru>=0.7.0

# LLM 集成
openai>=1.0.0
langchain>=0.1.0
langchain-openai>=0.0.5

# 向量数据库和嵌入
chromadb>=0.4.0
#sentence-transformers>=2.2.0 # 引入nvidia
faiss-cpu>=1.7.0

# 代码解析
tree-sitter>=0.20.0
tree-sitter-c>=0.20.0
tree-sitter-cpp>=0.20.0
clang>=16.0.0

# Web界面
fastapi>=0.100.0
uvicorn>=0.23.0
websockets>=11.0.0
jinja2>=3.1.0

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 测试
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0

# 开发工具
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0