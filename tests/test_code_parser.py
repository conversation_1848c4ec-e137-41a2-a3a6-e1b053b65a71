"""
代码解析器测试
"""

import pytest
import tempfile
from pathlib import Path

from ai_gdb.knowledge_base.code_parser import <PERSON>Parser, CodeSymbol, CodeChunk


class TestCodeParser:
    """代码解析器测试类"""
    
    def setup_method(self):
        """设置测试方法"""
        self.parser = CodeParser()
    
    def test_find_code_files(self):
        """测试查找代码文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建测试文件
            (temp_path / "test.c").write_text("int main() { return 0; }")
            (temp_path / "test.h").write_text("#ifndef TEST_H\n#define TEST_H\n#endif")
            (temp_path / "test.cpp").write_text("int main() { return 0; }")
            (temp_path / "test.txt").write_text("not a code file")
            
            # 创建子目录
            subdir = temp_path / "src"
            subdir.mkdir()
            (subdir / "utils.c").write_text("void utils() {}")
            
            # 解析项目
            chunks = self.parser.parse_project(temp_path)
            
            # 验证结果
            assert len(chunks) > 0
            
            # 检查文件路径
            file_paths = [chunk.file_path for chunk in chunks]
            assert any("test.c" in path for path in file_paths)
            assert any("test.h" in path for path in file_paths)
            assert any("test.cpp" in path for path in file_paths)
            assert any("utils.c" in path for path in file_paths)
            assert not any("test.txt" in path for path in file_paths)
    
    def test_parse_simple_c_file(self):
        """测试解析简单C文件"""
        c_code = """
#include <stdio.h>

int global_var = 10;

int add(int a, int b) {
    return a + b;
}

int main() {
    int result = add(5, 3);
    printf("Result: %d\\n", result);
    return 0;
}
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
            f.write(c_code)
            f.flush()
            
            try:
                chunks = self.parser.parse_file(Path(f.name))
                
                # 验证解析结果
                assert len(chunks) > 0
                
                # 检查符号
                all_symbols = []
                for chunk in chunks:
                    all_symbols.extend(chunk.symbols)
                
                symbol_names = [s.name for s in all_symbols]
                assert "add" in symbol_names or "main" in symbol_names
                
                # 检查依赖
                dependencies = []
                for chunk in chunks:
                    dependencies.extend(chunk.dependencies)
                
                assert "stdio.h" in dependencies
                
            finally:
                Path(f.name).unlink()
    
    def test_parse_cpp_file(self):
        """测试解析C++文件"""
        cpp_code = """
#include <iostream>
#include <vector>

class Calculator {
private:
    int value;
    
public:
    Calculator(int v) : value(v) {}
    
    int add(int x) {
        return value + x;
    }
    
    int getValue() const {
        return value;
    }
};

int main() {
    Calculator calc(10);
    std::cout << calc.add(5) << std::endl;
    return 0;
}
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.cpp', delete=False) as f:
            f.write(cpp_code)
            f.flush()
            
            try:
                chunks = self.parser.parse_file(Path(f.name))
                
                # 验证解析结果
                assert len(chunks) > 0
                
                # 检查符号
                all_symbols = []
                for chunk in chunks:
                    all_symbols.extend(chunk.symbols)
                
                symbol_names = [s.name for s in all_symbols]
                # 至少应该找到一些符号
                assert len(symbol_names) > 0
                
                # 检查依赖
                dependencies = []
                for chunk in chunks:
                    dependencies.extend(chunk.dependencies)
                
                assert "iostream" in dependencies
                assert "vector" in dependencies
                
            finally:
                Path(f.name).unlink()
    
    def test_extract_dependencies(self):
        """测试提取依赖关系"""
        code = """
#include <stdio.h>
#include <stdlib.h>
#include "local_header.h"
#include "utils/helper.h"

int main() {
    return 0;
}
"""
        
        dependencies = self.parser._extract_dependencies(code)
        
        expected_deps = ["stdio.h", "stdlib.h", "local_header.h", "utils/helper.h"]
        for dep in expected_deps:
            assert dep in dependencies
    
    def test_empty_file(self):
        """测试空文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
            f.write("")
            f.flush()
            
            try:
                chunks = self.parser.parse_file(Path(f.name))
                
                # 空文件应该返回一个空的代码块
                assert len(chunks) >= 0
                
            finally:
                Path(f.name).unlink()
    
    def test_malformed_code(self):
        """测试格式错误的代码"""
        malformed_code = """
#include <stdio.h>

int main( {
    printf("Hello World"
    return 0
}
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.c', delete=False) as f:
            f.write(malformed_code)
            f.flush()
            
            try:
                # 解析器应该能够处理格式错误的代码而不崩溃
                chunks = self.parser.parse_file(Path(f.name))
                
                # 即使代码有错误，也应该返回一些结果
                assert isinstance(chunks, list)
                
            finally:
                Path(f.name).unlink()
