"""
代码解析器测试
"""

import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from pathlib import Path

from src.knowledge_base.knowledge_base import KnowledgeBase
from src.config.config import Config

def test_search_code(config):
    knowledge_db = KnowledgeBase(config)
    symbols = knowledge_db.find_symbol("main")
    if not symbols:
        print("!!!No symbols found!!!")
        return
    for symbol in symbols:
        print(f"Symbol: {symbol.name}")
        print(f"File: {symbol.file_path}-{symbol.line_number}")
    
# main方法
if __name__ == "__main__":
    config = Config.from_env()
    test_search_code(config)