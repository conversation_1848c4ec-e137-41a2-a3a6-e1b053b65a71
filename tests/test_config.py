"""
配置模块测试
"""

import pytest
import tempfile
import os
from pathlib import Path

from ai_gdb.config import Config


class TestConfig:
    """配置测试类"""
    
    def test_config_creation(self):
        """测试配置创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_data = {
                "project_code_dir": temp_dir,
                "llm_api_key": "test_key"
            }
            
            config = Config(**config_data)
            
            assert config.project_code_dir == Path(temp_dir)
            assert config.llm_api_key == "test_key"
            assert config.llm_provider == "openai"  # 默认值
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试无效路径
        with pytest.raises(ValueError):
            Config(
                project_code_dir="/nonexistent/path",
                llm_api_key="test_key"
            )
        
        # 测试无效API密钥
        with tempfile.TemporaryDirectory() as temp_dir:
            with pytest.raises(ValueError):
                Config(
                    project_code_dir=temp_dir,
                    llm_api_key="your_api_key_here"  # 默认占位符
                )
    
    def test_config_from_env(self):
        """测试从环境变量加载配置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置环境变量
            os.environ["PROJECT_CODE_DIR"] = temp_dir
            os.environ["LLM_API_KEY"] = "test_key"
            os.environ["LLM_PROVIDER"] = "openai"
            
            try:
                config = Config.from_env()
                
                assert config.project_code_dir == Path(temp_dir)
                assert config.llm_api_key == "test_key"
                assert config.llm_provider == "openai"
                
            finally:
                # 清理环境变量
                del os.environ["PROJECT_CODE_DIR"]
                del os.environ["LLM_API_KEY"]
                del os.environ["LLM_PROVIDER"]
    
    def test_config_defaults(self):
        """测试默认配置值"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = Config(
                project_code_dir=temp_dir,
                llm_api_key="test_key"
            )
            
            assert config.llm_provider == "openai"
            assert config.llm_model_name == "gpt-4-turbo-preview"
            assert config.llm_temperature == 0.1
            assert config.max_debug_rounds == 10
            assert config.vector_db_type == "chroma"
