"""
集成测试
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from ai_gdb.config import Config
from ai_gdb.knowledge_base import KnowledgeBase
from ai_gdb.gdb_controller import GDBController
from ai_gdb.ai_agent import AIAgent


class TestIntegration:
    """集成测试类"""
    
    @pytest.fixture
    def temp_project(self):
        """创建临时项目"""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建示例C代码
            main_c = temp_path / "main.c"
            main_c.write_text("""
#include <stdio.h>
#include <stdlib.h>

int divide(int a, int b) {
    if (b == 0) {
        printf("Error: Division by zero!\\n");
        return -1;
    }
    return a / b;
}

int main() {
    int x = 10;
    int y = 0;  // 这里会导致除零错误
    
    int result = divide(x, y);
    printf("Result: %d\\n", result);
    
    return 0;
}
""")
            
            # 创建头文件
            utils_h = temp_path / "utils.h"
            utils_h.write_text("""
#ifndef UTILS_H
#define UTILS_H

int divide(int a, int b);

#endif
""")
            
            yield temp_path
    
    @pytest.fixture
    def mock_config(self, temp_project):
        """创建模拟配置"""
        return Config(
            project_code_dir=temp_project,
            llm_api_key="test_key",
            llm_provider="openai",
            vector_db_path=temp_project / "vector_db",
            log_file_path=temp_project / "test.log"
        )
    
    def test_knowledge_base_integration(self, mock_config):
        """测试知识库集成"""
        kb = KnowledgeBase(mock_config)
        
        # 测试解析项目
        chunks = kb.code_parser.parse_project(mock_config.project_code_dir)
        assert len(chunks) > 0
        
        # 检查是否找到了divide函数
        all_symbols = []
        for chunk in chunks:
            all_symbols.extend(chunk.symbols)
        
        symbol_names = [s.name for s in all_symbols]
        assert "divide" in symbol_names or "main" in symbol_names
    
    @pytest.mark.asyncio
    async def test_gdb_controller_mock(self, mock_config):
        """测试GDB控制器（模拟）"""
        # 由于GDB需要实际的可执行文件，这里使用模拟
        with patch('ai_gdb.gdb_controller.gdb_session.subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_process.poll.return_value = None
            mock_process.stdin = Mock()
            mock_process.stdout = Mock()
            mock_process.stderr = Mock()
            mock_popen.return_value = mock_process
            
            controller = GDBController(mock_config)
            
            # 测试启动会话
            success = await controller.start_debug_session()
            assert isinstance(success, bool)
    
    @pytest.mark.asyncio
    async def test_ai_agent_integration(self, mock_config):
        """测试AI Agent集成"""
        # 创建模拟组件
        kb = KnowledgeBase(mock_config)
        
        with patch('ai_gdb.gdb_controller.gdb_session.subprocess.Popen'):
            controller = GDBController(mock_config)
            
            # 模拟LLM客户端
            with patch('ai_gdb.ai_agent.llm_client.openai.AsyncOpenAI') as mock_openai:
                mock_client = Mock()
                mock_response = Mock()
                mock_response.choices = [Mock()]
                mock_response.choices[0].message.content = "这是一个测试响应"
                mock_response.usage = None
                
                mock_client.chat.completions.create = AsyncMock(return_value=mock_response)
                mock_openai.return_value = mock_client
                
                agent = AIAgent(mock_config, kb, controller)
                
                # 测试组件初始化
                assert agent.config == mock_config
                assert agent.knowledge_base == kb
                assert agent.gdb_controller == controller
                assert agent.llm_client is not None
                assert agent.tool_manager is not None
                assert agent.memory_manager is not None
    
    def test_config_integration(self, temp_project):
        """测试配置集成"""
        # 创建.env文件
        env_file = temp_project / ".env"
        env_content = f"""
PROJECT_CODE_DIR={temp_project}
LLM_API_KEY=test_key
LLM_PROVIDER=openai
VECTOR_DB_PATH={temp_project}/vector_db
LOG_LEVEL=DEBUG
"""
        env_file.write_text(env_content)
        
        # 从环境文件加载配置
        config = Config.from_env(str(env_file))
        
        assert config.project_code_dir == temp_project
        assert config.llm_api_key == "test_key"
        assert config.llm_provider == "openai"
        assert config.log_level == "DEBUG"
    
    def test_error_handling(self, mock_config):
        """测试错误处理"""
        # 测试无效配置
        with pytest.raises(ValueError):
            Config(
                project_code_dir="/nonexistent",
                llm_api_key="test_key"
            )
        
        # 测试知识库错误处理
        kb = KnowledgeBase(mock_config)
        
        # 测试搜索空查询
        results = kb.search_code("")
        assert isinstance(results, list)
        
        # 测试查找不存在的符号
        symbols = kb.find_symbol("nonexistent_function")
        assert isinstance(symbols, list)
        assert len(symbols) == 0
    
    @pytest.mark.asyncio
    async def test_memory_management(self, mock_config):
        """测试记忆管理"""
        from ai_gdb.ai_agent.memory_manager import MemoryManager
        
        memory_manager = MemoryManager(mock_config)
        
        # 测试会话管理
        session = memory_manager.start_session("test_session", "测试错误描述")
        assert session.session_id == "test_session"
        assert session.error_description == "测试错误描述"
        assert session.status == "active"
        
        # 测试添加记忆
        memory_id = memory_manager.add_memory(
            type="observation",
            content="测试观察",
            importance=0.8
        )
        assert memory_id != ""
        
        # 测试获取记忆
        recent_memories = memory_manager.get_recent_memories(count=5)
        assert len(recent_memories) > 0
        assert recent_memories[0].content == "测试观察"
        
        # 测试结束会话
        memory_manager.end_session("test_session", "completed", "测试完成")
        assert session.status == "completed"
        assert session.summary == "测试完成"
    
    def test_tool_manager_integration(self, mock_config):
        """测试工具管理器集成"""
        from ai_gdb.ai_agent.tool_manager import ToolManager
        from ai_gdb.knowledge_base import KnowledgeBase
        from ai_gdb.gdb_controller import GDBController
        
        kb = KnowledgeBase(mock_config)
        
        with patch('ai_gdb.gdb_controller.gdb_session.subprocess.Popen'):
            controller = GDBController(mock_config)
            tool_manager = ToolManager(controller, kb)
            
            # 测试工具注册
            tools = tool_manager.get_all_tools()
            assert len(tools) > 0
            
            # 检查特定工具
            tool_names = [tool.name for tool in tools]
            assert "set_breakpoint" in tool_names
            assert "search_code" in tool_names
            assert "get_variables" in tool_names
            
            # 测试工具模式生成
            schema = tool_manager.get_tools_schema()
            assert isinstance(schema, list)
            assert len(schema) > 0
            
            # 检查模式格式
            for tool_schema in schema:
                assert "type" in tool_schema
                assert "function" in tool_schema
                assert tool_schema["type"] == "function"
