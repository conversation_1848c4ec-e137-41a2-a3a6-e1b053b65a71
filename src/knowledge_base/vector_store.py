"""
向量存储模块
"""

import json
import pickle
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from loguru import logger

try:
    import chromadb
    from chromadb.config import Settings
    CHROMA_AVAILABLE = True
except ImportError:
    CHROMA_AVAILABLE = False
    logger.warning("ChromaDB不可用")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI不可用")

try:
    import faiss
    import numpy as np
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logger.warning("FAISS不可用")

from .code_parser import CodeChunk, CodeSymbol

@dataclass
class SearchResult:
    """搜索结果"""
    chunk: CodeChunk
    score: float
    metadata: Dict[str, Any]


class VectorStore:
    """向量存储基类"""
    
    def __init__(self, config):
        self.config = config
        self.embedding_model = None
        self._init_embedding_model()
    
    def _init_embedding_model(self):
        """初始化嵌入模型"""
        # 检查是否使用OpenAI兼容的嵌入模型
        self._init_openai_embedding()
        # if self.config.embedding_model.startswith('text-embedding'):
        #     self._init_openai_embedding()
        # else:
        #     self._init_local_embedding()

    def _init_openai_embedding(self):
        """初始化OpenAI兼容的嵌入模型"""
        if not OPENAI_AVAILABLE:
            logger.error("OpenAI不可用，无法初始化远程嵌入模型")
            return

        try:
            # 使用配置中的API设置
            openai.api_key = self.config.llm_api_key
            if hasattr(self.config, 'llm_base_url') and self.config.llm_base_url:
                openai.base_url = self.config.llm_base_url

            self.embedding_model = "openai"
            logger.info(f"OpenAI兼容嵌入模型初始化成功: {self.config.embedding_model}")
        except Exception as e:
            logger.error(f"OpenAI兼容嵌入模型初始化失败: {e}")
    
    def embed_text(self, text: str) -> List[float]:
        """文本嵌入"""
        if not self.embedding_model:
            return []

        try:
            if self.embedding_model == "openai":
                return self._embed_text_openai(text)
            else:
                embedding = self.embedding_model.encode(text)
                return embedding.tolist()
        except Exception as e:
            logger.error(f"文本嵌入失败: {e}")
            return []

    def _embed_text_openai(self, text: str) -> List[float]:
        """使用OpenAI兼容API进行文本嵌入"""
        try:
            response = openai.embeddings.create(
                model=self.config.embedding_model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"OpenAI嵌入失败: {e}")
            return []
    
    def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """批量文本嵌入"""
        if not self.embedding_model:
            return []

        try:
            if self.embedding_model == "openai":
                return self._embed_texts_openai(texts)
            else:
                embeddings = self.embedding_model.encode(texts)
                return embeddings.tolist()
        except Exception as e:
            logger.error(f"批量文本嵌入失败: {e}")
            return []

    def _embed_texts_openai(self, texts: List[str]) -> List[List[float]]:
        """使用OpenAI兼容API进行批量文本嵌入"""
        try:
            response = openai.embeddings.create(
                model=self.config.embedding_model,
                input=texts
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            logger.error(f"OpenAI批量嵌入失败: {e}")
            return []
    
    def add_chunks(self, chunks: List[CodeChunk]) -> None:
        """添加代码块"""
        raise NotImplementedError
    
    def search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """搜索相似代码块"""
        raise NotImplementedError
    
    def delete_all(self) -> None:
        """删除所有数据"""
        raise NotImplementedError

    def get_all_chunks(self) -> List[CodeChunk]:
        """获取所有代码块"""
        raise NotImplementedError


class ChromaVectorStore(VectorStore):
    """ChromaDB向量存储"""
    
    def __init__(self, config):
        super().__init__(config)
        self.client = None
        self.collection = None
        self._init_chroma()
    
    def _init_chroma(self):
        """初始化ChromaDB"""
        if not CHROMA_AVAILABLE:
            logger.error("ChromaDB不可用")
            return

        try:
            # 创建存储目录
            db_path = Path(self.config.vector_db_path)
            db_path.mkdir(parents=True, exist_ok=True)

            # 初始化客户端，禁用telemetry避免版本兼容问题
            self.client = chromadb.PersistentClient(
                path=str(db_path),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True,
                    is_persistent=True
                )
            )

            # 获取或创建集合
            try:
                self.collection = self.client.get_or_create_collection(
                    name="code_chunks",
                    metadata={"description": "C/C++代码块向量存储"}
                )
            except Exception as collection_error:
                logger.warning(f"获取集合失败，尝试重新创建: {collection_error}")
                # 如果集合有问题，尝试删除并重新创建
                try:
                    self.client.delete_collection("code_chunks")
                except:
                    pass
                self.collection = self.client.create_collection(
                    name="code_chunks",
                    metadata={"description": "C/C++代码块向量存储"}
                )

            logger.info(f"ChromaDB初始化成功: {db_path}")
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            self.client = None
            self.collection = None
    
    def add_chunks(self, chunks: List[CodeChunk]) -> None:
        """添加代码块到ChromaDB"""
        if not self.client:
            logger.error("ChromaDB客户端未初始化")
            return

        # 确保集合存在
        if not self.collection:
            try:
                self.collection = self.client.get_or_create_collection(
                    name="code_chunks",
                    metadata={"description": "C/C++代码块向量存储"}
                )
            except Exception as e:
                logger.error(f"无法创建或获取集合: {e}")
                return

        if not chunks:
            return
        
        try:
            # 准备数据
            documents = []
            metadatas = []
            ids = []
            
            for i, chunk in enumerate(chunks):
                # 创建文档内容
                doc_content = self._create_document_content(chunk)
                documents.append(doc_content)
                
                # 创建元数据 - ChromaDB只支持基本类型，需要将列表转换为字符串
                metadata = {
                    "file_path": chunk.file_path,
                    "start_line": chunk.start_line,
                    "end_line": chunk.end_line,
                    "symbols": ",".join([s.name for s in chunk.symbols]) if chunk.symbols else "",
                    "symbol_types": ",".join([s.type for s in chunk.symbols]) if chunk.symbols else "",
                    "dependencies": ",".join(chunk.dependencies) if chunk.dependencies else ""
                }
                metadatas.append(metadata)
                
                # 创建ID
                chunk_id = f"{Path(chunk.file_path).stem}_{chunk.start_line}_{chunk.end_line}_{i}"
                ids.append(chunk_id)
            
            # 添加到集合
            self.collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"成功添加 {len(chunks)} 个代码块到ChromaDB")
            
        except Exception as e:
            logger.error(f"添加代码块到ChromaDB失败: {e}")
    
    def search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """在ChromaDB中搜索"""
        if not self.client:
            logger.error("ChromaDB客户端未初始化")
            return []

        # 确保集合存在
        if not self.collection:
            try:
                self.collection = self.client.get_collection("code_chunks")
            except Exception as e:
                logger.error(f"无法获取集合: {e}")
                return []
        
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k
            )
            
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    # 重建CodeChunk对象
                    chunk = self._rebuild_chunk_from_metadata(doc, metadata)
                    
                    # 计算相似度分数 (距离越小，相似度越高)
                    score = 1.0 - distance
                    
                    search_result = SearchResult(
                        chunk=chunk,
                        score=score,
                        metadata=metadata
                    )
                    search_results.append(search_result)
            
            logger.debug(f"搜索查询 '{query}' 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"ChromaDB搜索失败: {e}")
            return []
    
    def delete_all(self) -> None:
        """删除ChromaDB中的所有数据"""
        if not self.client:
            logger.error("ChromaDB客户端未初始化")
            return

        try:
            # 删除集合
            try:
                self.client.delete_collection("code_chunks")
                logger.info("删除现有集合成功")
            except Exception as delete_error:
                logger.warning(f"删除集合失败（可能不存在）: {delete_error}")

            # 重新创建集合
            self.collection = self.client.create_collection(
                name="code_chunks",
                metadata={"description": "C/C++代码块向量存储"}
            )

            logger.info("ChromaDB数据清空并重新初始化成功")

        except Exception as e:
            logger.error(f"清空ChromaDB数据失败: {e}")
            # 如果清空失败，尝试重新初始化整个客户端
            self._init_chroma()

    def get_all_chunks(self) -> List[CodeChunk]:
        """获取ChromaDB中的所有代码块"""
        if not self.client or not self.collection:
            logger.warning("ChromaDB客户端或集合未初始化")
            return []

        try:
            # 获取集合中的所有数据
            results = self.collection.get()

            if not results or not results.get('documents'):
                logger.info("ChromaDB中没有数据")
                return []

            chunks = []
            documents = results['documents']
            metadatas = results.get('metadatas', [])

            for i, doc in enumerate(documents):
                metadata = metadatas[i] if i < len(metadatas) else {}
                try:
                    chunk = self._rebuild_chunk_from_metadata(doc, metadata)
                    chunks.append(chunk)
                except Exception as e:
                    logger.warning(f"重建代码块失败: {e}")
                    continue

            logger.info(f"从ChromaDB加载了 {len(chunks)} 个代码块")
            return chunks

        except Exception as e:
            logger.error(f"从ChromaDB获取所有代码块失败: {e}")
            return []

    def _create_document_content(self, chunk: CodeChunk) -> str:
        """创建文档内容"""
        content_parts = []
        
        # 添加文件路径
        content_parts.append(f"文件: {chunk.file_path}")
        
        # 添加符号信息
        if chunk.symbols:
            symbols_info = []
            for symbol in chunk.symbols:
                symbol_info = f"{symbol.type}: {symbol.name}"
                if symbol.signature:
                    symbol_info += f" ({symbol.signature})"
                symbols_info.append(symbol_info)
            content_parts.append("符号: " + ", ".join(symbols_info))
        
        # 添加依赖信息
        if chunk.dependencies:
            content_parts.append("依赖: " + ", ".join(chunk.dependencies))
        
        # 添加代码内容
        content_parts.append("代码:")
        content_parts.append(chunk.content)
        
        return "\n".join(content_parts)
    
    def _rebuild_chunk_from_metadata(self, doc: str, metadata: Dict[str, Any]) -> CodeChunk:
        """从元数据重建CodeChunk对象"""
        # 从文档中提取代码内容
        lines = doc.split('\n')
        code_start = -1
        for i, line in enumerate(lines):
            if line.strip() == "代码:":
                code_start = i + 1
                break
        
        if code_start >= 0:
            content = '\n'.join(lines[code_start:])
        else:
            content = doc
        
        # 重建符号列表 - 从字符串格式恢复
        symbols = []
        symbols_str = metadata.get('symbols', '')
        symbol_types_str = metadata.get('symbol_types', '')

        if symbols_str and symbol_types_str:
            symbol_names = symbols_str.split(',') if symbols_str else []
            symbol_types = symbol_types_str.split(',') if symbol_types_str else []

            for name, symbol_type in zip(symbol_names, symbol_types):
                if name.strip() and symbol_type.strip():  # 确保不是空字符串
                    symbol = CodeSymbol(
                        name=name.strip(),
                        type=symbol_type.strip(),
                        file_path=metadata['file_path'],
                        line_number=metadata['start_line'],
                        column=0
                    )
                    symbols.append(symbol)
        
        # 重建依赖列表 - 从字符串格式恢复
        dependencies_str = metadata.get('dependencies', '')
        dependencies = dependencies_str.split(',') if dependencies_str else []
        dependencies = [dep.strip() for dep in dependencies if dep.strip()]  # 去除空字符串和空格

        # 创建CodeChunk对象
        chunk = CodeChunk(
            content=content,
            file_path=metadata['file_path'],
            start_line=metadata['start_line'],
            end_line=metadata['end_line'],
            symbols=symbols,
            dependencies=dependencies
        )
        
        return chunk


class FAISSVectorStore(VectorStore):
    """FAISS向量存储"""
    
    def __init__(self, config):
        super().__init__(config)
        self.index = None
        self.chunks_metadata = []
        self._init_faiss()
    
    def _init_faiss(self):
        """初始化FAISS"""
        if not FAISS_AVAILABLE:
            logger.error("FAISS不可用")
            return
        
        try:
            # 创建存储目录
            db_path = Path(self.config.vector_db_path)
            db_path.mkdir(parents=True, exist_ok=True)
            
            # 尝试加载现有索引
            index_file = db_path / "faiss.index"
            metadata_file = db_path / "metadata.pkl"
            
            if index_file.exists() and metadata_file.exists():
                self.index = faiss.read_index(str(index_file))
                with open(metadata_file, 'rb') as f:
                    self.chunks_metadata = pickle.load(f)
                logger.info(f"加载现有FAISS索引: {index_file}")
            else:
                # 创建新索引 (假设嵌入维度为384)
                dimension = 384
                self.index = faiss.IndexFlatIP(dimension)  # 内积索引
                self.chunks_metadata = []
                logger.info("创建新的FAISS索引")
            
        except Exception as e:
            logger.error(f"FAISS初始化失败: {e}")
    
    def add_chunks(self, chunks: List[CodeChunk]) -> None:
        """添加代码块到FAISS"""
        if not self.index:
            logger.error("FAISS未初始化")
            return
        
        if not chunks:
            return
        
        try:
            # 创建文档内容并嵌入
            documents = [self._create_document_content(chunk) for chunk in chunks]
            embeddings = self.embed_texts(documents)
            
            if not embeddings:
                logger.error("嵌入生成失败")
                return
            
            # 转换为numpy数组
            embeddings_array = np.array(embeddings, dtype=np.float32)
            
            # 归一化向量 (用于内积索引)
            faiss.normalize_L2(embeddings_array)
            
            # 添加到索引
            self.index.add(embeddings_array)
            
            # 保存元数据
            for chunk in chunks:
                metadata = {
                    "file_path": chunk.file_path,
                    "start_line": chunk.start_line,
                    "end_line": chunk.end_line,
                    "content": chunk.content,
                    "symbols": [asdict(s) for s in chunk.symbols],
                    "dependencies": chunk.dependencies
                }
                self.chunks_metadata.append(metadata)
            
            # 保存索引和元数据
            self._save_index()
            
            logger.info(f"成功添加 {len(chunks)} 个代码块到FAISS")
            
        except Exception as e:
            logger.error(f"添加代码块到FAISS失败: {e}")
    
    def search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """在FAISS中搜索"""
        if not self.index or self.index.ntotal == 0:
            logger.warning("FAISS索引为空")
            return []
        
        try:
            # 嵌入查询
            query_embedding = self.embed_text(query)
            if not query_embedding:
                logger.error("查询嵌入失败")
                return []
            
            # 转换为numpy数组并归一化
            query_array = np.array([query_embedding], dtype=np.float32)
            faiss.normalize_L2(query_array)
            
            # 搜索
            scores, indices = self.index.search(query_array, min(top_k, self.index.ntotal))
            
            # 构建结果
            search_results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and idx < len(self.chunks_metadata):
                    metadata = self.chunks_metadata[idx]
                    chunk = self._rebuild_chunk_from_metadata(metadata)
                    
                    search_result = SearchResult(
                        chunk=chunk,
                        score=float(score),
                        metadata=metadata
                    )
                    search_results.append(search_result)
            
            logger.debug(f"搜索查询 '{query}' 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"FAISS搜索失败: {e}")
            return []
    
    def delete_all(self) -> None:
        """删除FAISS中的所有数据"""
        try:
            # 重新创建索引
            dimension = 384
            self.index = faiss.IndexFlatIP(dimension)
            self.chunks_metadata = []
            
            # 删除保存的文件
            db_path = Path(self.config.vector_db_path)
            index_file = db_path / "faiss.index"
            metadata_file = db_path / "metadata.pkl"
            
            if index_file.exists():
                index_file.unlink()
            if metadata_file.exists():
                metadata_file.unlink()
            
            logger.info("FAISS数据清空成功")
            
        except Exception as e:
            logger.error(f"清空FAISS数据失败: {e}")

    def get_all_chunks(self) -> List[CodeChunk]:
        """获取FAISS中的所有代码块"""
        if not self.chunks_metadata:
            logger.info("FAISS中没有元数据")
            return []

        try:
            chunks = []
            for metadata in self.chunks_metadata:
                try:
                    chunk = self._rebuild_chunk_from_metadata(metadata)
                    chunks.append(chunk)
                except Exception as e:
                    logger.warning(f"重建代码块失败: {e}")
                    continue

            logger.info(f"从FAISS加载了 {len(chunks)} 个代码块")
            return chunks

        except Exception as e:
            logger.error(f"从FAISS获取所有代码块失败: {e}")
            return []

    def _save_index(self):
        """保存索引和元数据"""
        try:
            db_path = Path(self.config.vector_db_path)
            db_path.mkdir(parents=True, exist_ok=True)
            
            # 保存索引
            index_file = db_path / "faiss.index"
            faiss.write_index(self.index, str(index_file))
            
            # 保存元数据
            metadata_file = db_path / "metadata.pkl"
            with open(metadata_file, 'wb') as f:
                pickle.dump(self.chunks_metadata, f)
            
        except Exception as e:
            logger.error(f"保存FAISS索引失败: {e}")
    
    def _create_document_content(self, chunk: CodeChunk) -> str:
        """创建文档内容"""
        content_parts = []
        
        # 添加文件路径
        content_parts.append(f"文件: {chunk.file_path}")
        
        # 添加符号信息
        if chunk.symbols:
            symbols_info = []
            for symbol in chunk.symbols:
                symbol_info = f"{symbol.type}: {symbol.name}"
                if symbol.signature:
                    symbol_info += f" ({symbol.signature})"
                symbols_info.append(symbol_info)
            content_parts.append("符号: " + ", ".join(symbols_info))
        
        # 添加依赖信息
        if chunk.dependencies:
            content_parts.append("依赖: " + ", ".join(chunk.dependencies))
        
        # 添加代码内容
        content_parts.append("代码:")
        content_parts.append(chunk.content)
        
        return "\n".join(content_parts)
    
    def _rebuild_chunk_from_metadata(self, metadata: Dict[str, Any]) -> CodeChunk:
        """从元数据重建CodeChunk对象"""
        # 重建符号列表
        symbols = []
        for symbol_dict in metadata.get('symbols', []):
            symbol = CodeSymbol(**symbol_dict)
            symbols.append(symbol)
        
        # 创建CodeChunk对象
        chunk = CodeChunk(
            content=metadata['content'],
            file_path=metadata['file_path'],
            start_line=metadata['start_line'],
            end_line=metadata['end_line'],
            symbols=symbols,
            dependencies=metadata.get('dependencies', [])
        )
        
        return chunk


def create_vector_store(config) -> VectorStore:
    """创建向量存储实例"""
    if config.vector_db_type == "chroma":
        return ChromaVectorStore(config)
    elif config.vector_db_type == "faiss":
        return FAISSVectorStore(config)
    else:
        raise ValueError(f"不支持的向量数据库类型: {config.vector_db_type}")
