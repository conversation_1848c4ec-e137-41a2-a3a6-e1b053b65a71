"""
LLM客户端
"""

import json
import random
import re
import os
import time
from typing import List, Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from loguru import logger

from ..utils.debug_logger import DebugLogger
from .mock_client import MockClient

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI库不可用")


@dataclass
class Message:
    """消息"""
    role: str  # system, user, assistant
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    """LLM响应"""
    content: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class LLMClient:
    """LLM客户端"""
    
    def __init__(self, config):
        self.config = config
        self.client = None
        self.debug_logger = DebugLogger(config)
        self._init_client()
    
    def _init_client(self):
        """初始化LLM客户端"""
        try:
            if self.config.llm_provider == "openai":
                self._init_openai_client()
            elif self.config.llm_provider == "mock":
                self._init_mock_client()
            else:
                raise ValueError(f"不支持的LLM提供商: {self.config.llm_provider}")

        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            raise
    
    def _init_openai_client(self):
        """初始化OpenAI客户端"""
        if not OPENAI_AVAILABLE:
            raise RuntimeError("OpenAI库不可用")
        
        client_kwargs = {
            "api_key": self.config.llm_api_key,
            "timeout": 50
        }
        
        if self.config.llm_base_url:
            client_kwargs["base_url"] = self.config.llm_base_url
        
        self.client = openai.AsyncOpenAI(**client_kwargs)
        logger.info("OpenAI客户端初始化成功")

    def _init_mock_client(self):
        """初始化Mock客户端"""
        self.mock_client = MockClient(self.config)
    
    async def chat_completion(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> LLMResponse:
        """聊天完成"""
        try:
            if self.config.llm_provider == "openai":
                return await self._openai_chat_completion(messages, tools, stream)
            elif self.config.llm_provider == "mock":
                return await self.mock_client.chat_completion(messages, tools, stream)
            else:
                raise ValueError(f"不支持的LLM提供商: {self.config.llm_provider}")

        except Exception as e:
            logger.error(f"LLM聊天完成失败: {e}")
            raise
    
    async def _openai_chat_completion(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> LLMResponse:
        """OpenAI聊天完成"""
        # 获取最后一条用户消息，加nothink
        for msg in reversed(messages):
            if msg.role == "user":
                msg.content = f"{msg.content} {self.config.llm_nothink}"
                break
        start_time = time.time()

        # 转换消息格式
        openai_messages = [
            {"role": msg.role, "content": msg.content}
            for msg in messages
        ]

        # 构建请求参数
        request_params = {
            "model": self.config.llm_model_name,
            "messages": openai_messages,
            "max_tokens": self.config.llm_max_tokens,
            "temperature": self.config.llm_temperature,
            "stream": stream
        }

        # 添加工具
        if tools:
            request_params["tools"] = tools
            request_params["tool_choice"] = "auto"

        # 记录请求
        self.debug_logger.log_llm_request(
            self.config.llm_provider,
            self.config.llm_model_name,
            openai_messages,
            tools
        )

        try:
            # 发送请求
            response = await self.client.chat.completions.create(**request_params)

            response_time = time.time() - start_time

            if stream:
                # 流式响应处理
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content

                # 记录响应
                self.debug_logger.log_llm_response(
                    self.config.llm_provider,
                    self.config.llm_model_name,
                    content,
                    None,
                    response_time
                )

                return LLMResponse(content=content)
            else:
                # 非流式响应
                content = response.choices[0].message.content
                usage = response.usage.dict() if response.usage else None

                # 记录响应
                self.debug_logger.log_llm_response(
                    self.config.llm_provider,
                    self.config.llm_model_name,
                    content,
                    usage,
                    response_time
                )

                return LLMResponse(
                    content=content,
                    usage=usage
                )

        except Exception as e:
            response_time = time.time() - start_time

            # 记录错误
            self.debug_logger.log_llm_error(
                self.config.llm_provider,
                self.config.llm_model_name,
                str(e),
                request_params
            )

            raise

    async def generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入"""
        try:
            if self.config.llm_provider == "openai":
                response = await self.client.embeddings.create(
                    model="text-embedding-ada-002",
                    input=text
                )
                return response.data[0].embedding
            elif self.config.llm_provider == "mock":
                # 生成Mock嵌入向量（1536维，与OpenAI兼容）
                return [random.uniform(-1, 1) for _ in range(1536)]
            else:
                logger.warning(f"嵌入生成不支持提供商: {self.config.llm_provider}")
                return []

        except Exception as e:
            logger.error(f"生成嵌入失败: {e}")
            return []
    
    def create_system_prompt(self, context: Dict[str, Any]) -> str:
        """创建系统提示"""
        prompt_parts = [
            "你是一个专业的C/C++调试专家，具有丰富的GDB调试经验。",
            "你的任务是分析程序崩溃和错误，并提供精确的调试建议。",
            "",
            "你有以下能力：",
            "1. 分析调用栈和变量状态",
            "2. 设置和管理断点",
            "3. 单步调试程序执行",
            "4. 检查内存状态",
            "5. 搜索相关代码",
            "",
        ]

        # 添加可用工具列表
        if context.get("available_tools"):
            prompt_parts.extend([
                "你可以使用以下工具来协助调试：",
                ""
            ])

            tools = context["available_tools"]
            for i, tool in enumerate(tools, 1):
                if isinstance(tool, dict):
                    tool_name = tool.get("name", "未知工具")
                    tool_desc = tool.get("description", "无描述")
                    prompt_parts.append(f"{i}. {tool_name}: {tool_desc}")
                else:
                    # 如果是Tool对象
                    prompt_parts.append(f"{i}. {tool.name}: {tool.description}")

            prompt_parts.extend([
                "",
                "请根据当前情况选择合适的工具来进行调试分析。",
                "在回复中明确说明你要使用哪些工具以及使用的原因。",
                ""
            ])

        prompt_parts.append("请根据提供的信息进行分析，并给出具体的调试步骤。")
        
        # 添加上下文信息
        if context.get("project_info"):
            prompt_parts.extend([
                "",
                f"项目信息: {context['project_info']}"
            ])
        
        if context.get("error_description"):
            prompt_parts.extend([
                "",
                f"错误描述: {context['error_description']}"
            ])
        
        if context.get("debug_state"):
            prompt_parts.extend([
                "",
                f"当前调试状态: {context['debug_state']}"
            ])
        
        return "\n".join(prompt_parts)
    
    def create_debugging_prompt(
        self, 
        error_description: str,
        debug_state: Optional[Dict[str, Any]] = None,
        code_context: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """创建调试提示"""
        prompt_parts = [
            f"程序出现以下错误: {error_description}",
            ""
        ]
        
        # 添加调试状态
        if debug_state:
            prompt_parts.extend([
                "当前调试状态:",
                f"- 程序运行状态: {'运行中' if debug_state.get('is_running') else '已停止'}",
                f"- 停止原因: {debug_state.get('stop_reason', '未知')}"
            ])
            
            # 添加当前栈帧
            if debug_state.get('current_frame'):
                frame = debug_state['current_frame']
                prompt_parts.extend([
                    f"- 当前函数: {frame.get('function', '未知')}",
                    f"- 文件位置: {frame.get('file', '未知')}:{frame.get('line', '未知')}"
                ])
            
            # 添加变量信息
            if debug_state.get('variables'):
                prompt_parts.append("- 局部变量:")
                for name, var in debug_state['variables'].items():
                    prompt_parts.append(f"  {name} = {var.get('value', '未知')}")
            
            prompt_parts.append("")
        
        # 添加代码上下文
        if code_context:
            prompt_parts.extend([
                "相关代码上下文:",
                ""
            ])
            
            for i, context in enumerate(code_context[:3]):  # 限制显示前3个
                prompt_parts.extend([
                    f"代码块 {i+1}:",
                    f"文件: {context.get('file_path', '未知')}",
                    f"函数: {', '.join(context.get('symbols', []))}",
                    "代码:",
                    "```c",
                    context.get('content', ''),
                    "```",
                    ""
                ])
        
        prompt_parts.extend([
            "请分析这个错误，并提供以下信息:",
            "1. 可能的错误原因",
            "2. 建议的调试步骤",
            "3. 需要检查的变量和内存位置",
            "4. 应该设置的断点位置",
            "",
            "请给出具体、可执行的调试建议。"
        ])
        
        return "\n".join(prompt_parts)
    
    def parse_tool_calls(self, response_content: str) -> List[Dict[str, Any]]:
        """解析工具调用"""
        tool_calls = []
        
        try:
            # 尝试解析JSON格式的工具调用
            if "```json" in response_content:
                json_blocks = response_content.split("```json")[1:]
                for block in json_blocks:
                    json_content = block.split("```")[0].strip()
                    try:
                        tool_call = json.loads(json_content)
                        tool_calls.append(tool_call)
                    except json.JSONDecodeError:
                        continue
            
            # 解析函数调用格式
            import re
            function_pattern = r'(\w+)\((.*?)\)'
            matches = re.findall(function_pattern, response_content)
            
            for func_name, args_str in matches:
                if func_name in ['set_breakpoint', 'step_over', 'step_into', 'continue_execution', 'evaluate_expression']:
                    tool_call = {
                        "function": func_name,
                        "arguments": args_str.strip()
                    }
                    tool_calls.append(tool_call)
            
        except Exception as e:
            logger.error(f"解析工具调用失败: {e}")
        
        return tool_calls
