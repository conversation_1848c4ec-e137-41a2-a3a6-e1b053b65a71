"""
记忆管理器
"""

import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from loguru import logger


@dataclass
class MemoryEntry:
    """记忆条目"""
    id: str
    type: str  # conversation, action, observation, hypothesis
    content: str
    metadata: Dict[str, Any]
    timestamp: float
    importance: float = 0.5  # 重要性评分 0-1
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MemoryEntry":
        return cls(**data)


@dataclass
class DebuggingSession:
    """调试会话"""
    session_id: str
    start_time: float
    end_time: Optional[float]
    error_description: str
    status: str  # active, completed, failed
    memories: List[MemoryEntry]
    summary: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "error_description": self.error_description,
            "status": self.status,
            "memories": [memory.to_dict() for memory in self.memories],
            "summary": self.summary
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DebuggingSession":
        memories = [MemoryEntry.from_dict(m) for m in data.get("memories", [])]
        return cls(
            session_id=data["session_id"],
            start_time=data["start_time"],
            end_time=data.get("end_time"),
            error_description=data["error_description"],
            status=data["status"],
            memories=memories,
            summary=data.get("summary")
        )


class MemoryManager:
    """记忆管理器"""
    
    def __init__(self, config):
        self.config = config
        self.current_session: Optional[DebuggingSession] = None
        self.sessions: Dict[str, DebuggingSession] = {}
        self.memory_file = Path(config.vector_db_path) / "memory.json"
        self.max_memories_per_session = 100
        self.max_sessions = 50
        
        # 确保存储目录存在
        self.memory_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载历史记忆
        self._load_memories()
    
    def start_session(self, session_id: str, error_description: str) -> DebuggingSession:
        """开始新的调试会话"""
        logger.info(f"开始调试会话: {session_id}")
        
        session = DebuggingSession(
            session_id=session_id,
            start_time=time.time(),
            end_time=None,
            error_description=error_description,
            status="active",
            memories=[]
        )
        
        self.current_session = session
        self.sessions[session_id] = session
        
        # 添加会话开始记忆
        self.add_memory(
            type="conversation",
            content=f"开始调试会话: {error_description}",
            metadata={"action": "session_start"},
            importance=0.8
        )
        
        return session
    
    def end_session(self, session_id: str, status: str = "completed", summary: Optional[str] = None):
        """结束调试会话"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.end_time = time.time()
            session.status = status
            session.summary = summary
            
            # 添加会话结束记忆
            self.add_memory(
                type="conversation",
                content=f"调试会话结束: {status}",
                metadata={"action": "session_end", "summary": summary},
                importance=0.9
            )
            
            if self.current_session and self.current_session.session_id == session_id:
                self.current_session = None
            
            # 保存记忆
            self._save_memories()
            
            logger.info(f"调试会话结束: {session_id}, 状态: {status}")
    
    def add_memory(
        self, 
        type: str, 
        content: str, 
        metadata: Optional[Dict[str, Any]] = None,
        importance: float = 0.5
    ) -> str:
        """添加记忆"""
        if not self.current_session:
            logger.warning("没有活跃的调试会话")
            return ""
        
        memory_id = f"{self.current_session.session_id}_{len(self.current_session.memories)}"
        
        memory = MemoryEntry(
            id=memory_id,
            type=type,
            content=content,
            metadata=metadata or {},
            timestamp=time.time(),
            importance=importance
        )
        
        self.current_session.memories.append(memory)
        
        # 限制记忆数量
        if len(self.current_session.memories) > self.max_memories_per_session:
            self._prune_memories()
        
        logger.debug(f"添加记忆: {type} - {content[:50]}...")
        
        return memory_id
    
    def add_action_memory(self, action: str, result: Any, success: bool = True) -> str:
        """添加动作记忆"""
        return self.add_memory(
            type="action",
            content=f"执行动作: {action}",
            metadata={
                "action": action,
                "result": str(result)[:200],  # 限制结果长度
                "success": success
            },
            importance=0.7 if success else 0.8
        )
    
    def add_observation_memory(self, observation: str, context: Optional[Dict[str, Any]] = None) -> str:
        """添加观察记忆"""
        return self.add_memory(
            type="observation",
            content=observation,
            metadata=context or {},
            importance=0.6
        )
    
    def add_hypothesis_memory(self, hypothesis: str, confidence: float = 0.5) -> str:
        """添加假设记忆"""
        return self.add_memory(
            type="hypothesis",
            content=hypothesis,
            metadata={"confidence": confidence},
            importance=0.8
        )
    
    def get_recent_memories(self, count: int = 10, types: Optional[List[str]] = None) -> List[MemoryEntry]:
        """获取最近的记忆"""
        if not self.current_session:
            return []
        
        memories = self.current_session.memories
        
        # 按类型过滤
        if types:
            memories = [m for m in memories if m.type in types]
        
        # 按时间排序并返回最近的
        memories.sort(key=lambda x: x.timestamp, reverse=True)
        return memories[:count]
    
    def get_important_memories(self, threshold: float = 0.7, count: int = 20) -> List[MemoryEntry]:
        """获取重要记忆"""
        if not self.current_session:
            return []
        
        # 过滤重要记忆
        important_memories = [
            m for m in self.current_session.memories 
            if m.importance >= threshold
        ]
        
        # 按重要性排序
        important_memories.sort(key=lambda x: x.importance, reverse=True)
        return important_memories[:count]
    
    def search_memories(self, query: str, session_id: Optional[str] = None) -> List[MemoryEntry]:
        """搜索记忆"""
        memories = []
        
        if session_id:
            if session_id in self.sessions:
                memories = self.sessions[session_id].memories
        else:
            # 搜索所有会话
            for session in self.sessions.values():
                memories.extend(session.memories)
        
        # 简单的文本搜索
        query_lower = query.lower()
        matching_memories = [
            m for m in memories 
            if query_lower in m.content.lower()
        ]
        
        # 按重要性和时间排序
        matching_memories.sort(key=lambda x: (x.importance, x.timestamp), reverse=True)
        
        return matching_memories[:20]  # 限制返回数量
    
    def get_session_summary(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """获取会话摘要"""
        session = None
        if session_id:
            session = self.sessions.get(session_id)
        else:
            session = self.current_session
        
        if not session:
            return {}
        
        # 统计记忆类型
        memory_stats = {}
        for memory in session.memories:
            memory_stats[memory.type] = memory_stats.get(memory.type, 0) + 1
        
        # 获取重要事件
        important_events = [
            m.content for m in session.memories 
            if m.importance >= 0.8
        ]
        
        return {
            "session_id": session.session_id,
            "error_description": session.error_description,
            "status": session.status,
            "duration": (session.end_time or time.time()) - session.start_time,
            "memory_count": len(session.memories),
            "memory_stats": memory_stats,
            "important_events": important_events[:10],
            "summary": session.summary
        }
    
    def get_context_for_llm(self, max_memories: int = 15) -> str:
        """获取用于LLM的上下文"""
        if not self.current_session:
            return "没有活跃的调试会话。"
        
        context_parts = [
            f"当前调试会话: {self.current_session.session_id}",
            f"错误描述: {self.current_session.error_description}",
            ""
        ]
        
        # 获取重要记忆和最近记忆的组合
        important_memories = self.get_important_memories(threshold=0.7, count=max_memories // 2)
        recent_memories = self.get_recent_memories(count=max_memories // 2)
        
        # 合并并去重
        all_memories = important_memories + recent_memories
        seen_ids = set()
        unique_memories = []
        for memory in all_memories:
            if memory.id not in seen_ids:
                unique_memories.append(memory)
                seen_ids.add(memory.id)
        
        # 按时间排序
        unique_memories.sort(key=lambda x: x.timestamp)
        
        if unique_memories:
            context_parts.append("相关记忆:")
            for memory in unique_memories[-max_memories:]:
                time_str = time.strftime("%H:%M:%S", time.localtime(memory.timestamp))
                context_parts.append(f"[{time_str}] {memory.type}: {memory.content}")
        
        return "\n".join(context_parts)
    
    def _prune_memories(self):
        """修剪记忆（保留重要的记忆）"""
        if not self.current_session:
            return
        
        memories = self.current_session.memories
        
        # 按重要性排序
        memories.sort(key=lambda x: x.importance, reverse=True)
        
        # 保留最重要的记忆
        important_count = int(self.max_memories_per_session * 0.7)
        important_memories = memories[:important_count]
        
        # 保留最近的记忆
        recent_count = self.max_memories_per_session - important_count
        memories.sort(key=lambda x: x.timestamp, reverse=True)
        recent_memories = memories[:recent_count]
        
        # 合并并去重
        all_memories = important_memories + recent_memories
        seen_ids = set()
        unique_memories = []
        for memory in all_memories:
            if memory.id not in seen_ids:
                unique_memories.append(memory)
                seen_ids.add(memory.id)
        
        self.current_session.memories = unique_memories
        logger.info(f"修剪记忆: 保留 {len(unique_memories)} 条记忆")
    
    def _save_memories(self):
        """保存记忆到文件"""
        try:
            # 限制保存的会话数量
            if len(self.sessions) > self.max_sessions:
                # 保留最近的会话
                sessions_by_time = sorted(
                    self.sessions.values(),
                    key=lambda x: x.start_time,
                    reverse=True
                )
                self.sessions = {
                    s.session_id: s for s in sessions_by_time[:self.max_sessions]
                }
            
            # 保存到JSON文件
            data = {
                "sessions": {
                    session_id: session.to_dict()
                    for session_id, session in self.sessions.items()
                }
            }
            
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"记忆已保存到: {self.memory_file}")
            
        except Exception as e:
            logger.error(f"保存记忆失败: {e}")
    
    def _load_memories(self):
        """从文件加载记忆"""
        try:
            if self.memory_file.exists():
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                sessions_data = data.get("sessions", {})
                for session_id, session_data in sessions_data.items():
                    session = DebuggingSession.from_dict(session_data)
                    self.sessions[session_id] = session
                
                logger.info(f"加载 {len(self.sessions)} 个历史调试会话")
            
        except Exception as e:
            logger.error(f"加载记忆失败: {e}")
    
    def get_historical_patterns(self) -> Dict[str, Any]:
        """获取历史模式分析"""
        patterns = {
            "common_errors": {},
            "successful_strategies": [],
            "failed_strategies": [],
            "average_session_duration": 0,
            "success_rate": 0
        }
        
        if not self.sessions:
            return patterns
        
        total_duration = 0
        successful_sessions = 0
        
        for session in self.sessions.values():
            # 统计常见错误
            error_key = session.error_description[:50]  # 使用前50个字符作为键
            patterns["common_errors"][error_key] = patterns["common_errors"].get(error_key, 0) + 1
            
            # 计算持续时间
            if session.end_time:
                duration = session.end_time - session.start_time
                total_duration += duration
            
            # 统计成功率
            if session.status == "completed":
                successful_sessions += 1
                
                # 提取成功策略
                for memory in session.memories:
                    if memory.type == "action" and memory.metadata.get("success"):
                        action = memory.metadata.get("action")
                        if action and action not in patterns["successful_strategies"]:
                            patterns["successful_strategies"].append(action)
            
            elif session.status == "failed":
                # 提取失败策略
                for memory in session.memories:
                    if memory.type == "action" and not memory.metadata.get("success"):
                        action = memory.metadata.get("action")
                        if action and action not in patterns["failed_strategies"]:
                            patterns["failed_strategies"].append(action)
        
        # 计算平均值
        if self.sessions:
            patterns["average_session_duration"] = total_duration / len(self.sessions)
            patterns["success_rate"] = successful_sessions / len(self.sessions)
        
        return patterns
