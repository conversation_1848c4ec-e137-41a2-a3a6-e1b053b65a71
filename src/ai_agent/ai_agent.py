"""
AI Agent主模块
"""

import asyncio
import uuid
import time
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger

from .llm_client import LLMClient, Message, LLMResponse
from .tool_manager import <PERSON><PERSON><PERSON>ana<PERSON>, ToolCall, ToolResult
from .memory_manager import MemoryManager, DebuggingSession
from ..utils.debug_logger import Debug<PERSON>ogger,print_console


class AIAgent:
    """AI调试代理"""

    def __init__(self, config, knowledge_base, gdb_controller):
        self.config = config
        self.knowledge_base = knowledge_base
        self.gdb_controller = gdb_controller

        # 初始化组件
        self.llm_client = LLMClient(config)
        self.tool_manager = ToolManager(gdb_controller, knowledge_base)
        self.memory_manager = MemoryManager(config)
        self.debug_logger = DebugLogger(config)

        # 状态管理
        self.current_session: Optional[DebuggingSession] = None
        self.is_debugging = False
        self.max_debug_rounds = config.max_debug_rounds
        self.current_round = 0

        # 事件广播回调
        self.event_broadcaster = None

    def set_event_broadcaster(self, broadcaster):
        """设置事件广播器"""
        self.event_broadcaster = broadcaster

    async def _broadcast_debug_output(self, message: str, output_type: str = "info"):
        """广播调试输出"""
        if output_type == "debug":
            logger.debug(message)
        elif output_type == "info":
            logger.info(message)
        elif output_type == "error":
            logger.error(message)
        elif output_type == "warning":
            logger.warning(message)
        if self.event_broadcaster:
            await self.event_broadcaster({
                "type": "debug_output",
                "output": message,
                "output_type": output_type,
                "round": self.current_round,
                "timestamp": asyncio.get_event_loop().time()
            })
    
    async def start_debug_session(self, fault_description: str) -> Dict[str, Any]:
        """开始调试会话（立即返回，后台执行）"""
        try:
            logger.info(f"开始AI调试会话: {fault_description}")

            # 检查是否已有调试会话在运行
            if self.is_debugging:
                return {"success": False, "error": "调试会话已在进行中"}

            # 生成会话ID
            session_id = str(uuid.uuid4())[:8]

            # 启动记忆管理
            self.current_session = self.memory_manager.start_session(session_id, fault_description)

            # 设置调试状态
            self.is_debugging = True
            self.current_round = 0

            # 在后台启动调试会话
            asyncio.create_task(self._run_debug_session_background(fault_description))

            return {"success": True, "message": "调试会话已启动", "session_id": session_id}

        except Exception as e:
            logger.error(f"启动调试会话失败: {e}")
            self.is_debugging = False
            if self.current_session:
                self.memory_manager.end_session(self.current_session.session_id, "failed", str(e))
            return {"success": False, "error": str(e)}

    async def _run_debug_session_background(self, fault_description: str):
        """在后台运行调试会话"""
        try:
            logger.info(f"后台调试会话开始: {fault_description}")

            # 启动GDB会话
            if not await self.gdb_controller.start_debug_session():
                logger.error("GDB会话启动失败")
                self.is_debugging = False
                return

            # 运行程序（如果有测试脚本）
            # if self.config.test_script_path:
            #     await self._run_test_script()

            # 开始调试循环
            result = await self._debug_loop(fault_description)

            # 结束会话
            status = "completed" if result.get("success") else "failed"
            if self.current_session:
                self.memory_manager.end_session(self.current_session.session_id, status, result.get("summary"))

            logger.info(f"调试会话完成: {result}")

        except Exception as e:
            logger.error(f"后台调试会话失败: {e}")
            if self.current_session:
                self.memory_manager.end_session(self.current_session.session_id, "failed", str(e))

        finally:
            # 清理资源
            await self.gdb_controller.stop_debug_session()
            self.current_session = None
            self.is_debugging = False
            self.current_round = 0
        
    async def _debug_loop(self, fault_description: str) -> Dict[str, Any]:
        """调试主循环 - 重构版本：每轮次都要复现错误"""
        self.is_debugging = True
        self.current_round = 0

        while self.is_debugging and self.current_round < self.max_debug_rounds:
            self.current_round += 1
            logger.info(f"开始调试轮次 {self.current_round}/{self.max_debug_rounds}")

            try:
                # 每轮次的完整调试流程
                round_result = await self._execute_debug_round(fault_description)

                # 检查是否找到了错误原因
                if round_result.get("error_found"):
                    logger.info(f"第{self.current_round}轮次找到错误原因")
                    return {
                        "success": True,
                        "solution": round_result.get("solution"),
                        "summary": round_result.get("summary"),
                        "rounds": self.current_round,
                        "error_cause": round_result.get("error_cause")
                    }

                # 检查是否成功复现了错误
                if round_result.get("error_reproduced"):
                    logger.info(f"第{self.current_round}轮次成功复现错误，但未找到根本原因")
                    # 记录这轮的发现，继续下一轮
                    self.memory_manager.add_observation_memory(
                        f"第{self.current_round}轮次复现错误但未找到根本原因",
                        round_result
                    )
                else:
                    logger.warning(f"第{self.current_round}轮次未能复现错误")
                    # 调整调试策略
                    await self._adjust_debug_strategy(round_result)

                # 短暂休眠
                await asyncio.sleep(1.0)

            except Exception as e:
                logger.error(f"调试轮次 {self.current_round} 失败: {e}")
                self.memory_manager.add_observation_memory(
                    f"调试轮次失败: {e}",
                    {"round": self.current_round, "error": str(e)}
                )

        # 调试结束
        self.is_debugging = False

        if self.current_round >= self.max_debug_rounds:
            logger.warning("达到最大调试轮次，未能找到错误原因")
            return {
                "success": False,
                "error": "达到最大调试轮次",
                "summary": f"经过{self.current_round}轮次调试，未能找到错误根本原因",
                "rounds": self.current_round
            }

        return {
            "success": False,
            "error": "调试过程中断",
            "rounds": self.current_round
        }

    async def _execute_debug_round(self, fault_description: str) -> Dict[str, Any]:
        """执行一轮完整的调试流程"""
        logger.info(f"执行第{self.current_round}轮调试")
        await self._broadcast_debug_output(f"开始第{self.current_round}轮调试", "info")

        round_result = {
            "round": self.current_round,
            "error_reproduced": False,
            "error_found": False,
            "solution": None,
            "summary": "",
            "error_cause": None,
            "debug_steps": []
        }
        #  分析代码,获取初始断点
        await self._initial_analysis(fault_description)

        try:
            #0. 如果是调试程序自身，则需要先设置初始断点
            await self._broadcast_debug_output("步骤0: 设置初始断点", "info")
            await self._set_breakpoints(self.initial_breakpoints)

            # 1. 运行程序尝试复现错误
            await self._broadcast_debug_output("步骤1: 运行程序复现错误", "info")
            # 使用配置的参数的启动程序
            args = []
            if self.config.target_args:
                args = self.config.target_args.split()
            success = await self.gdb_controller.run_program(args)
            if not success:
                run_result = {"error_occurred":True, "reason": "程序启动失败"}
            else:
                run_result = {"error_occurred":False, "reason": "程序运行成功"}
            round_result["debug_steps"].append({"step": "run_program", "result": run_result})
                      
            script_result = {}
            if self.config.test_script_path:
                # 2. 有测试脚本，则需要等服务端启动后设置断点
                # 检查服务程序的端口
                # await self._set_breakpoints(self.initial_breakpoints)
                # 再运行测试脚本
                script_result = await self._execute_test_script()
                if not script_result.get("success"):
                    await self._broadcast_debug_output(f"测试脚本执行失败: {script_result.get('error')}", "warning")

            while True:
                await asyncio.sleep(1.0)
                # 检查测试程序是否结束
                if script_result and script_result.get("process",None):
                    process = script_result.get["process"]
                    if process.poll() is not None:
                        await self._broadcast_debug_output(f"测试脚本执行完毕!", "break")
                        run_result = {
                            "error_occurred": False,
                            "reason": "测试脚本执行完毕，程序正常退出",
                            "test_script_executed": True,
                            "round_completed": True
                        }
                        # 终止服务进程，结束本轮调试
                        await self.gdb_controller.execute_gdb_command("kill")
                        break
                
                # 检查gdb调试程序状态
                debug_state = await self.gdb_controller.get_debug_state()
                if debug_state and not debug_state.is_stopped:
                    # 继续运行程序
                    continue

                # 程序停止，可能遇到错误或断点
                # 2. 分析错误状态
                logger.info("步骤2: 分析错误状态")
                analysis_result = await self._analyze_current_state(debug_state)
                round_result["debug_steps"].append({"step": "analyze_state", "result": analysis_result})

                # 3. 检查变量和内存状态
                logger.info("步骤3: 检查变量和内存状态")
                memory_analysis = await self._analyze_memory_state(debug_state)
                round_result["debug_steps"].append({"step": "memory_analysis", "result": memory_analysis})

                # 4. 检查调用栈
                logger.info("步骤4: 检查调用栈")
                stack_analysis = await self._analyze_call_stack()
                round_result["debug_steps"].append({"step": "stack_analysis", "result": stack_analysis})

                # 5. 使用LLM综合分析所有信息
                logger.info("步骤5: LLM综合分析")
                comprehensive_analysis = await self._llm_error_analysis(
                    fault_description, debug_state, analysis_result,
                    memory_analysis, stack_analysis
                )
                round_result["debug_steps"].append({"step": "llm_analysis", "result": comprehensive_analysis})

                # 6. 判断是否找到错误原因
                if comprehensive_analysis.get("error_cause_identified"):
                    round_result["error_found"] = True
                    round_result["error_cause"] = comprehensive_analysis.get("error_cause")
                    round_result["solution"] = comprehensive_analysis.get("solution")
                    round_result["summary"] = f"找到错误原因: {round_result['error_cause']}"
                else:
                    # 7. 如果未找到原因，进行单步调试
                    logger.info("步骤6: 单步调试分析")
                    step_debug_result = await self._step_debug_analysis()
                    round_result["debug_steps"].append({"step": "step_debug", "result": step_debug_result})

                    round_result["summary"] = "复现错误但未找到根本原因，需要进一步调试"
            
            self._clean_breakpoints()
            
        except Exception as e:
            logger.error(f"调试轮次执行失败: {e}")
            round_result["summary"] = f"调试轮次执行失败: {e}"

        return round_result

    async def _restart_gdb_session(self) -> Dict[str, Any]:
        """重启目标服务进程"""
        try:
            logger.info("重启目标服务进程")

            # 停止当前GDB会话
            await self.gdb_controller.stop_debug_session()

            # 短暂等待确保进程完全停止
            await asyncio.sleep(1.0)

            # 重新启动GDB会话
            if not await self.gdb_controller.start_debug_session():
                logger.error("GDB会话重启失败")
                return {"success": False, "error": "GDB会话重启失败"}

            logger.info("目标服务进程重启成功")
            return {"success": True, "message": "目标服务进程重启成功"}

        except Exception as e:
            logger.error(f"重启目标服务进程失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_test_script(self) -> Dict[str, Any]:
        """执行测试脚本"""
        try:
            if not self.config.test_script_path:
                return {"success": False, "error": "未配置测试脚本路径"}

            script_path = Path(self.config.test_script_path)
            if not script_path.exists():
                return {"success": False, "error": f"测试脚本不存在: {script_path}"}

            logger.info(f"执行测试脚本: {script_path}")
            start_time = time.time()

            # 根据脚本类型执行
            if script_path.suffix == '.py':
                # Python脚本
                test_process = await asyncio.create_subprocess_exec(
                    'python', str(script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.config.project_code_dir
                )
            elif script_path.suffix == '.sh':
                # Shell脚本
                test_process = await asyncio.create_subprocess_exec(
                    'bash', str(script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.config.project_code_dir
                )
            else:
                # 直接执行
                test_process = await asyncio.create_subprocess_exec(
                    str(script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.config.project_code_dir
                )
            stdout_thread = threading.Thread(
                target=print_console,
                args=(test_process.stdout, "TEST-STDOUT"),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=print_console,
                args=(test_process.stderr, "TEST-STDERR"),
                daemon=True
            )
            stdout_thread.start()
            stderr_thread.start()
            
            return {"success": True,
                    "process": test_process }

            # 等待脚本执行完成（最多30秒）
            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30.0)

                stdout_text = stdout.decode('utf-8') if stdout else ""
                stderr_text = stderr.decode('utf-8') if stderr else ""
                execution_time = time.time() - start_time

                # 记录测试脚本执行
                self.debug_logger.log_test_script_execution(
                    str(script_path),
                    stdout_text,
                    stderr_text,
                    process.returncode,
                    execution_time
                )

                if process.returncode == 0:
                    logger.info("测试脚本执行成功")
                    return {
                        "success": True,
                        "stdout": stdout_text,
                        "stderr": stderr_text,
                        "return_code": process.returncode
                    }
                else:
                    logger.warning(f"测试脚本执行失败，返回码: {process.returncode}")
                    return {
                        "success": False,
                        "error": f"脚本执行失败，返回码: {process.returncode}",
                        "stdout": stdout_text,
                        "stderr": stderr_text,
                        "return_code": process.returncode
                    }

            except asyncio.TimeoutError:
                # 超时，终止进程
                process.terminate()
                await process.wait()
                return {"success": False, "error": "测试脚本执行超时"}

        except Exception as e:
            logger.error(f"执行测试脚本失败: {e}")
            return {"success": False, "error": str(e)}

    async def _adjust_debug_strategy(self, round_result: Dict[str, Any]):
        """调整调试策略"""
        logger.info("调整调试策略")

        try:
            # 基于上一轮的结果调整断点策略
            if not round_result.get("error_reproduced"):
                # 如果没有复现错误，可能需要调整断点位置
                logger.info("调整断点策略：添加更多断点")
                await self._add_more_breakpoints()

            # 重置程序状态
            await self.gdb_controller.restart_program()

        except Exception as e:
            logger.error(f"调整调试策略失败: {e}")

    async def _run_program_with_monitoring(self) -> Dict[str, Any]:
        """运行程序并监控错误（包括执行测试脚本）"""
        try:
            # 启动程序，使用配置的参数
            args = []
            if self.config.target_args:
                args = self.config.target_args.split()

            success = await self.gdb_controller.run_program(args)
            if not success:
                return {"error_occurred": False, "reason": "程序启动失败"}

            # 如果配置了测试脚本，执行测试脚本
            if self.config.test_script_path:
                logger.info("执行测试脚本以重现问题")
                await self._broadcast_debug_output("执行测试脚本以重现问题", "info")

                script_result = await self._execute_test_script()
                if not script_result.get("success"):
                    logger.warning(f"测试脚本执行失败: {script_result.get('error')}")
                    await self._broadcast_debug_output(f"测试脚本执行失败: {script_result.get('error')}", "warning")

                    # 广播错误输出
                    if script_result.get("stderr"):
                        await self._broadcast_debug_output(f"测试脚本错误输出:\n{script_result.get('stderr')}", "error")
                else:
                    logger.info("测试脚本执行完成")
                    await self._broadcast_debug_output("测试脚本执行完成", "info")

                    # 广播标准输出
                    if script_result.get("stdout"):
                        await self._broadcast_debug_output(f"测试脚本输出:\n{script_result.get('stdout')}", "output")

                    # 广播标准错误（如果有）
                    if script_result.get("stderr"):
                        await self._broadcast_debug_output(f"测试脚本错误输出:\n{script_result.get('stderr')}", "warning")

            # 等待程序执行和测试脚本完成
            await asyncio.sleep(3.0)

            # 检查程序状态
            debug_state = await self.gdb_controller.get_debug_state()

            if debug_state and debug_state.is_stopped:
                # 程序停止，可能遇到错误
                return {
                    "error_occurred": True,
                    "stop_reason": debug_state.stop_reason,
                    "current_frame": debug_state.current_frame.__dict__ if debug_state.current_frame else None,
                    "test_script_executed": bool(self.config.test_script_path)
                }
            else:
                # 程序正常运行或已退出
                # 如果执行了测试脚本，认为这是一个调试轮次的结束
                if self.config.test_script_path:
                    # 测试脚本执行完毕，可以结束服务进程
                    logger.info("测试脚本执行完毕，结束服务进程")
                    await self._broadcast_debug_output("测试脚本执行完毕，结束服务进程", "info")
                    await self.gdb_controller.execute_gdb_command("kill")

                    return {
                        "error_occurred": False,
                        "reason": "测试脚本执行完毕，程序正常退出",
                        "test_script_executed": True,
                        "round_completed": True
                    }
                else:
                    return {
                        "error_occurred": False,
                        "reason": "程序正常运行",
                        "test_script_executed": False
                    }

        except Exception as e:
            logger.error(f"运行程序监控失败: {e}")
            return {"error_occurred": False, "reason": f"监控失败: {e}"}
    
    async def _run_test_script(self) -> Dict[str, Any]:
        """运行程序并监控错误（包括执行测试脚本）"""
        try:
            # 如果配置了测试脚本，执行测试脚本
                logger.info("执行测试脚本以重现问题")
                await self._broadcast_debug_output("执行测试脚本以重现问题", "info")

                script_result = await self._execute_test_script()
                if not script_result.get("success"):
                    logger.warning(f"测试脚本执行失败: {script_result.get('error')}")
                    await self._broadcast_debug_output(f"测试脚本执行失败: {script_result.get('error')}", "warning")

                    # 广播错误输出
                    if script_result.get("stderr"):
                        await self._broadcast_debug_output(f"测试脚本错误输出:\n{script_result.get('stderr')}", "error")
                else:
                    logger.info("测试脚本执行完成")
                    await self._broadcast_debug_output("测试脚本执行完成", "info")

                    # 广播标准输出
                    if script_result.get("stdout"):
                        await self._broadcast_debug_output(f"测试脚本输出:\n{script_result.get('stdout')}", "output")

                    # 广播标准错误（如果有）
                    if script_result.get("stderr"):
                        await self._broadcast_debug_output(f"测试脚本错误输出:\n{script_result.get('stderr')}", "warning")

                return script_result

        except Exception as e:
            logger.error(f"运行测试脚本失败: {e}")
            return {"error_occurred": False, "reason": f"测试失败: {e}"}

    async def _analyze_memory_state(self, debug_state) -> Dict[str, Any]:
        """分析内存状态"""
        memory_analysis = {
            "variables": {},
            "pointers": {},
            "memory_issues": []
        }

        try:
            if debug_state and debug_state.variables:
                for var_name, var_info in debug_state.variables.items():
                    memory_analysis["variables"][var_name] = {
                        "value": var_info.value,
                        "type": var_info.type if hasattr(var_info, 'type') else "unknown"
                    }

                    # 检查指针相关问题
                    if "ptr" in var_name.lower() or "*" in str(var_info.value):
                        memory_analysis["pointers"][var_name] = var_info.value

                        # 检查空指针
                        if var_info.value in ["0x0", "NULL", "(nil)"]:
                            memory_analysis["memory_issues"].append({
                                "type": "null_pointer",
                                "variable": var_name,
                                "description": f"变量 {var_name} 是空指针"
                            })

        except Exception as e:
            logger.error(f"内存状态分析失败: {e}")
            memory_analysis["error"] = str(e)

        return memory_analysis

    async def _analyze_call_stack(self) -> Dict[str, Any]:
        """分析调用栈"""
        try:
            frames = await self.gdb_controller.get_backtrace()

            stack_analysis = {
                "frame_count": len(frames),
                "frames": [],
                "potential_issues": []
            }

            for i, frame in enumerate(frames):
                frame_info = {
                    "level": i,
                    "function": frame.function,
                    "file": frame.file,
                    "line": frame.line
                }
                stack_analysis["frames"].append(frame_info)

                # 检查可疑的函数调用
                if frame.function in ["malloc", "free", "strcpy", "strcat", "sprintf"]:
                    stack_analysis["potential_issues"].append({
                        "type": "risky_function",
                        "function": frame.function,
                        "location": f"{frame.file}:{frame.line}",
                        "description": f"调用了可能有风险的函数: {frame.function}"
                    })

            return stack_analysis

        except Exception as e:
            logger.error(f"调用栈分析失败: {e}")
            return {"error": str(e)}

    async def _llm_error_analysis(
        self, fault_description: str, debug_state, analysis_result: Dict,
        memory_analysis: Dict, stack_analysis: Dict
    ) -> Dict[str, Any]:
        """使用LLM进行错误分析"""
        try:
            # 构建分析上下文
            context = {
                "debug_state": debug_state.__dict__ if debug_state else {},
                "memory_analysis": memory_analysis,
                "stack_analysis": stack_analysis,
                "available_tools": self.tool_manager.get_all_tools()
            }

            # 创建错误分析提示
            error_prompt = self._create_error_analysis_prompt(
                fault_description, debug_state, analysis_result, memory_analysis, stack_analysis
            )

            messages = [
                Message(
                    role="system",
                    content=self.llm_client.create_system_prompt(context)
                ),
                Message(
                    role="user",
                    content=error_prompt
                )
            ]

            # 调用LLM
            response = await self.llm_client.chat_completion(
                messages=messages,
                tools=self.tool_manager.get_tools_schema()
            )

            # 解析LLM响应
            return self._parse_error_analysis_response(response.content)

        except Exception as e:
            logger.error(f"LLM错误分析失败: {e}")
            return {"error": str(e), "error_cause_identified": False}

    async def _step_debug_analysis(self) -> Dict[str, Any]:
        """单步调试分析"""
        try:
            step_results = []

            # 执行几步单步调试
            for i in range(5):
                step_success = await self.gdb_controller.step_over()
                if not step_success:
                    break

                debug_state = await self.gdb_controller.get_debug_state()
                step_results.append({
                    "step": i + 1,
                    "success": step_success,
                    "state": debug_state.__dict__ if debug_state else None
                })

                # 短暂休眠
                await asyncio.sleep(0.5)

            return {
                "steps_executed": len(step_results),
                "step_results": step_results
            }

        except Exception as e:
            logger.error(f"单步调试分析失败: {e}")
            return {"error": str(e)}

    async def _add_more_breakpoints(self):
        """添加更多断点"""
        try:
            # 首先尝试设置一些通用的函数断点
            common_functions = ["main", "malloc", "free", "printf"]

            for func_name in common_functions:
                bp_id = await self.gdb_controller.set_breakpoint(func_name)
                if bp_id:
                    logger.info(f"添加函数断点: {func_name}")
                else:
                    logger.warning(f"添加函数断点失败: {func_name}")

            # 基于知识库搜索添加更多断点（使用文件名而不是完整路径）
            try:
                symbols = self.knowledge_base.find_symbol("main")
                for symbol in symbols[:2]:  # 最多2个符号
                    # 只使用文件名
                    file_name = symbol.file_path.split('/')[-1] if '/' in symbol.file_path else symbol.file_path
                    location = f"{file_name}:{symbol.line_number}"
                    bp_id = await self.gdb_controller.set_breakpoint(location)
                    if bp_id:
                        logger.info(f"添加断点: {location}")
                    else:
                        logger.warning(f"添加断点失败: {location}")
            except Exception as e:
                logger.debug(f"基于知识库添加断点失败: {e}")

        except Exception as e:
            logger.error(f"添加断点失败: {e}")

    def _create_error_analysis_prompt(
        self, fault_description: str, debug_state, analysis_result: Dict,
        memory_analysis: Dict, stack_analysis: Dict
    ) -> str:
        """创建错误分析提示"""
        prompt_parts = [
            "请分析以下C/C++程序的错误信息，并判断错误的根本原因：",
            "",
            f"错误描述: {fault_description}",
            ""
        ]

        # 添加程序状态信息
        if debug_state:
            prompt_parts.extend([
                "程序当前状态:",
                f"- 程序状态: {'运行中' if debug_state.is_running else '已停止'}",
                f"- 停止原因: {debug_state.stop_reason}",
            ])

            if debug_state.current_frame:
                frame = debug_state.current_frame
                prompt_parts.extend([
                    f"- 当前函数: {frame.function}",
                    f"- 文件位置: {frame.file}:{frame.line}",
                ])

        # 添加内存分析信息
        if memory_analysis.get("memory_issues"):
            prompt_parts.extend([
                "",
                "发现的内存问题:",
            ])
            for issue in memory_analysis["memory_issues"]:
                prompt_parts.append(f"- {issue['type']}: {issue['description']}")

        # 添加调用栈信息
        if stack_analysis.get("frames"):
            prompt_parts.extend([
                "",
                "调用栈信息:",
            ])
            for frame in stack_analysis["frames"][:5]:  # 显示前5层
                prompt_parts.append(f"- #{frame['level']}: {frame['function']} ({frame['file']}:{frame['line']})")

        if stack_analysis.get("potential_issues"):
            prompt_parts.extend([
                "",
                "调用栈中的潜在问题:",
            ])
            for issue in stack_analysis["potential_issues"]:
                prompt_parts.append(f"- {issue['description']}")

        prompt_parts.extend([
            "",
            "请回答以下问题：",
            "1. 根据以上信息，错误的根本原因是什么？",
            "2. 这个错误是否已经可以确定原因？(回答 true 或 false)",
            "3. 如果可以确定，请提供具体的解决方案。",
            "4. 如果不能确定，还需要检查哪些方面？",
            "",
            "请以结构化的方式回答，明确标明是否找到了错误原因。"
        ])

        return "\n".join(prompt_parts)

    def _parse_error_analysis_response(self, response: str) -> Dict[str, Any]:
        """解析错误分析响应"""
        result = {
            "error_cause_identified": False,
            "error_cause": None,
            "solution": None,
            "next_steps": []
        }

        try:
            lines = response.split('\n')

            # 查找是否确定了错误原因
            for line in lines:
                line_lower = line.lower()
                if "true" in line_lower and ("确定" in line_lower or "找到" in line_lower):
                    result["error_cause_identified"] = True
                    break
                elif "false" in line_lower and ("确定" in line_lower or "找到" in line_lower):
                    result["error_cause_identified"] = False
                    break

            # 提取错误原因
            in_cause_section = False
            for line in lines:
                if "根本原因" in line or "错误原因" in line:
                    in_cause_section = True
                    continue

                if in_cause_section and line.strip():
                    if not line.startswith(('2.', '3.', '4.')):
                        result["error_cause"] = line.strip()
                        break

            # 提取解决方案
            in_solution_section = False
            for line in lines:
                if "解决方案" in line or "solution" in line.lower():
                    in_solution_section = True
                    continue

                if in_solution_section and line.strip():
                    if not line.startswith(('4.')):
                        result["solution"] = line.strip()
                        break

        except Exception as e:
            logger.error(f"解析错误分析响应失败: {e}")

        return result

    async def _initial_analysis(self, fault_description: str):
        """初始分析 - 第一阶段：了解项目结构和错误上下文"""
        logger.info("开始第一阶段分析：项目结构和错误上下文分析")

        # 1. 项目结构分析
        project_analysis = await self._analyze_project_structure()

        # 2. 错误相关代码搜索
        search_results = self.knowledge_base.search_code(fault_description, top_k=10)

        # 3. 符号和函数分析
        symbol_analysis = await self._analyze_relevant_symbols(fault_description, search_results)

        # 4. 获取调试建议
        suggestions = self.knowledge_base.get_debugging_suggestions(fault_description)

        # 5. 使用LLM进行综合分析
        comprehensive_analysis = await self._llm_comprehensive_analysis(
            fault_description, project_analysis, search_results, symbol_analysis, suggestions
        )

        # 6. 记录分析结果
        self.memory_manager.add_observation_memory(
            f"第一阶段分析完成：项目结构分析、代码搜索({len(search_results)}个结果)、符号分析、LLM综合分析",
            {
                "project_analysis": project_analysis,
                "search_results_count": len(search_results),
                "symbol_analysis": symbol_analysis,
                "suggestions_count": len(suggestions),
                "comprehensive_analysis": comprehensive_analysis,
                "fault_description": fault_description
            }
        )

        # 7. 记录初始断点
        self.initial_breakpoints = comprehensive_analysis.get("suggested_breakpoints", [])

        logger.info("第一阶段分析完成")

    async def _analyze_project_structure(self) -> Dict[str, Any]:
        """分析项目结构"""
        try:
            # 获取项目基本信息
            project_info = {
                "project_dir": str(self.config.project_code_dir),
                "target_executable": str(self.config.target_executable) if self.config.target_executable else None,
                "target_args": self.config.target_args,
                "main_files": [],
                "header_files": [],
                "source_files": []
            }

            # 搜索主要文件
            main_symbols = self.knowledge_base.find_symbol("main")
            if main_symbols:
                project_info["main_files"] = [
                    {"file": symbol.file_path, "line": symbol.line_number}
                    for symbol in main_symbols[:3]
                ]

            # 获取文件统计信息
            all_files = self.knowledge_base.get_all_files()
            if all_files:
                project_info["source_files"] = [f for f in all_files if f.endswith(('.c', '.cpp', '.cc'))][:10]
                project_info["header_files"] = [f for f in all_files if f.endswith(('.h', '.hpp'))][:10]

            return project_info

        except Exception as e:
            logger.error(f"项目结构分析失败: {e}")
            return {"error": str(e)}

    async def _analyze_relevant_symbols(self, fault_description: str, search_results) -> Dict[str, Any]:
        """分析相关符号"""
        try:
            symbol_analysis = {
                "relevant_functions": [],
                "relevant_variables": [],
                "potential_issues": []
            }

            # 从搜索结果中提取符号
            for result in search_results[:5]:
                if hasattr(result, 'chunk') and hasattr(result.chunk, 'symbols'):
                    for symbol in result.chunk.symbols:
                        if symbol.type == "function":
                            symbol_analysis["relevant_functions"].append({
                                "name": symbol.name,
                                "file": symbol.file_path,
                                "line": symbol.line_number
                            })
                        elif symbol.type in ["variable", "parameter"]:
                            symbol_analysis["relevant_variables"].append({
                                "name": symbol.name,
                                "file": symbol.file_path,
                                "line": symbol.line_number
                            })

            # 基于错误描述查找特定符号
            error_keywords = ["malloc", "free", "null", "pointer", "segfault", "crash"]
            for keyword in error_keywords:
                if keyword.lower() in fault_description.lower():
                    symbols = self.knowledge_base.find_symbol(keyword)
                    if symbols:
                        symbol_analysis["potential_issues"].append({
                            "keyword": keyword,
                            "symbols": [{"name": s.name, "file": s.file_path, "line": s.line_number} for s in symbols[:3]]
                        })

            return symbol_analysis

        except Exception as e:
            logger.error(f"符号分析失败: {e}")
            return {"error": str(e)}

    async def _llm_comprehensive_analysis(
        self, fault_description: str, project_analysis: Dict,
        search_results, symbol_analysis: Dict, suggestions
    ) -> Dict[str, Any]:
        """使用LLM进行综合分析"""
        try:
            # 构建分析上下文
            context = {
                "project_analysis": project_analysis,
                "symbol_analysis": symbol_analysis,
                "available_tools": self.tool_manager.get_all_tools()
            }

            # 创建分析提示
            analysis_prompt = self._create_comprehensive_analysis_prompt(
                fault_description, project_analysis, search_results, symbol_analysis, suggestions
            )

            messages = [
                Message(
                    role="system",
                    content=self.llm_client.create_system_prompt(context)
                ),
                Message(
                    role="user",
                    content=f"{analysis_prompt}"
                )
            ]
            # 调用LLM
            response = await self.llm_client.chat_completion(
                messages=messages,
                tools=self.tool_manager.get_tools_schema()
            )

            return {
                "llm_analysis": response.content,
                "suggested_breakpoints": self._extract_breakpoints_from_response(response.content),
                "next_steps": self._extract_next_steps_from_response(response.content)
            }

        except Exception as e:
            logger.error(f"LLM综合分析失败: {e}")
            return {"error": str(e)}

    async def _set_breakpoints(self, breakpoints: Any):
        """基于分析结果设置初始断点"""
        try:
            # 首先尝试设置一些基本的函数断点
            # basic_functions = ["main"]
            # self.bp_ids = []
            # for func_name in basic_functions:
            #     bp_id = await self.gdb_controller.set_breakpoint(func_name)
            #     if bp_id:
            #         logger.info(f"设置基本函数断点成功: {func_name}")
            #         self.bp_ids.append(bp_id)
            #     else:
            #         logger.warning(f"设置基本函数断点失败: {func_name}")

            # 然后处理分析建议的断点
            for bp in breakpoints[:3]:  # 限制最多3个分析断点
                if isinstance(bp, dict):
                    file_path = bp.get("file")
                    line_number = bp.get("line")
                    if file_path and line_number:
                        # 只使用文件名，不使用完整路径
                        file_name = file_path.split('/')[-1] if '/' in file_path else file_path
                        location = f"{file_name}:{line_number}"
                        bp_id = await self.gdb_controller.set_breakpoint(location)
                        if bp_id:
                            logger.info(f"设置断点成功: {location}")
                            self.bp_ids.append(bp_id)
                        else:
                            logger.warning(f"设置断点失败: {location}")

        except Exception as e:
            logger.error(f"设置初始断点失败: {e}")
    
    async def _clean_breakpoints(self):
        """取消所有断点"""
        if self.bp_ids:
            for bp_id in self.bp_ids:
                await self.gdb_controller.remove_breakpoint(bp_id)
        
    def _create_comprehensive_analysis_prompt(
        self, fault_description: str, project_analysis: Dict,
        search_results, symbol_analysis: Dict, suggestions
    ) -> str:
        """创建综合分析提示"""
        prompt_parts = [
            "请对以下C/C++项目进行综合分析，以便开始调试：",
            "",
            f"错误描述: {fault_description}",
            "",
            "项目结构信息:",
        ]

        # 添加项目信息
        if project_analysis.get("main_files"):
            prompt_parts.append("主要文件:")
            for main_file in project_analysis["main_files"]:
                prompt_parts.append(f"  - {main_file['file']}:{main_file['line']}")

        if project_analysis.get("source_files"):
            prompt_parts.append(f"源文件数量: {len(project_analysis['source_files'])}")
            prompt_parts.append(f"头文件数量: {len(project_analysis.get('header_files', []))}")

        # 添加搜索结果
        prompt_parts.extend([
            "",
            f"相关代码搜索结果 (共{len(search_results)}个):"
        ])

        for i, result in enumerate(search_results[:3], 1):
            if hasattr(result, 'chunk'):
                chunk = result.chunk
                prompt_parts.extend([
                    f"{i}. {chunk.file_path}:{chunk.start_line}-{chunk.end_line} (相似度: {result.score:.3f})",
                    f"   内容预览: {chunk.content[:200]}..."
                ])

        # 添加符号分析
        if symbol_analysis.get("relevant_functions"):
            prompt_parts.extend([
                "",
                "相关函数:",
            ])
            for func in symbol_analysis["relevant_functions"][:5]:
                prompt_parts.append(f"  - {func['name']} ({func['file']}:{func['line']})")

        if symbol_analysis.get("potential_issues"):
            prompt_parts.extend([
                "",
                "潜在问题关键词:",
            ])
            for issue in symbol_analysis["potential_issues"]:
                prompt_parts.append(f"  - {issue['keyword']}: {len(issue['symbols'])} 个相关符号")

        prompt_parts.extend([
            "",
            "请分析以上信息并回答：",
            "1. 根据错误描述和代码结构，最可能的错误原因是什么？",
            "2. 应该在哪些关键位置设置断点？(请提供具体的文件名和行号)",
            "3. 调试的优先级顺序应该是什么？",
            "4. 需要重点检查哪些变量和内存状态？",
            "",
            "请以结构化的方式回答，包含具体的文件路径和行号。"
        ])

        return "\n".join(prompt_parts)

    def _extract_breakpoints_from_response(self, response: str) -> List[Dict[str, Any]]:
        """从LLM响应中提取断点信息"""
        breakpoints = []
        try:
            lines = response.split('\n')
            for line in lines:
                # 查找包含文件路径和行号的模式
                if ':' in line and any(ext in line for ext in ['.c', '.cpp', '.h', '.hpp']):
                    # 简单的正则匹配 file.c:123 格式
                    import re
                    pattern = r'([^/\s]+\.[ch]p?p?):(\d+)'
                    matches = re.findall(pattern, line)
                    for file_path, line_num in matches:
                        breakpoints.append({
                            "file": file_path,
                            "line": int(line_num),
                            "reason": line.strip()
                        })
        except Exception as e:
            logger.error(f"提取断点信息失败: {e}")

        return breakpoints

    def _extract_next_steps_from_response(self, response: str) -> List[str]:
        """从LLM响应中提取下一步行动"""
        next_steps = []
        try:
            lines = response.split('\n')
            in_steps_section = False
            for line in lines:
                line = line.strip()
                if any(keyword in line.lower() for keyword in ['步骤', 'step', '行动', 'action', '优先级']):
                    in_steps_section = True
                    continue

                if in_steps_section and line:
                    if line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '*')):
                        next_steps.append(line)
                    elif not line[0].isdigit() and len(next_steps) > 0:
                        # 如果不是数字开头且已经有步骤了，可能是新的段落
                        break
        except Exception as e:
            logger.error(f"提取下一步行动失败: {e}")

        return next_steps

    async def _set_suggested_breakpoint(self, suggestion: Dict[str, Any], action: str):
        """根据建议设置断点"""
        try:
            code_chunk = suggestion.get("code_chunk")
            if code_chunk and code_chunk.symbols:
                # 在函数入口设置断点
                for symbol in code_chunk.symbols:
                    if symbol.type == "function":
                        bp_id = await self.gdb_controller.set_breakpoint(symbol.name)
                        if bp_id:
                            self.memory_manager.add_action_memory(
                                f"设置断点在函数 {symbol.name}",
                                {"breakpoint_id": bp_id, "location": symbol.name},
                                success=True
                            )
                            break
        except Exception as e:
            logger.error(f"设置建议断点失败: {e}")
    
    async def _analyze_current_state(self, debug_state) -> Dict[str, Any]:
        """分析当前调试状态"""
        if not debug_state:
            return {"analysis": "无法获取调试状态"}
        
        # 构建分析上下文
        context = {
            "debug_state": debug_state.__dict__,
            "memory_context": self.memory_manager.get_context_for_llm(),
            "current_round": self.current_round,
            "available_tools": self.tool_manager.get_all_tools()
        }
        
        # 创建分析提示
        messages = [
            Message(
                role="system",
                content=self.llm_client.create_system_prompt(context)
            ),
            Message(
                role="user",
                content=self._create_state_analysis_prompt(debug_state)
            )
        ]
        
        # 获取LLM分析
        try:
            response = await self.llm_client.chat_completion(messages)
            analysis = response.content
            
            self.memory_manager.add_observation_memory(
                f"状态分析: {analysis[:200]}...",
                {"full_analysis": analysis, "round": self.current_round}
            )
            
            # 解析分析结果
            return self._parse_analysis_response(analysis)
            
        except Exception as e:
            logger.error(f"状态分析失败: {e}")
            return {"analysis": f"分析失败: {e}"}
    
    async def _plan_next_action(self, debug_state, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """规划下一步行动"""
        # 构建规划上下文
        context = {
            "debug_state": debug_state.__dict__ if debug_state else {},
            "analysis": analysis_result,
            "available_tools": self.tool_manager.get_all_tools(),
            "memory_context": self.memory_manager.get_context_for_llm()
        }
        
        # 创建规划提示
        messages = [
            Message(
                role="system",
                content=self.llm_client.create_system_prompt(context)
            ),
            Message(
                role="user",
                content=self._create_action_planning_prompt(debug_state, analysis_result)
            )
        ]
        
        # 添加工具定义
        tools = self.tool_manager.get_tools_schema()
        
        try:
            response = await self.llm_client.chat_completion(messages, tools=tools)
            
            # 解析工具调用
            tool_calls = self.llm_client.parse_tool_calls(response.content)
            
            action_plan = {
                "reasoning": response.content,
                "tool_calls": tool_calls
            }
            
            self.memory_manager.add_hypothesis_memory(
                f"行动计划: {len(tool_calls)} 个工具调用",
                confidence=0.7
            )
            
            return action_plan
            
        except Exception as e:
            logger.error(f"行动规划失败: {e}")
            return {"error": str(e)}
    
    async def _execute_action_plan(self, action_plan: Dict[str, Any]) -> Dict[str, Any]:
        """执行行动计划"""
        if "error" in action_plan:
            return {"success": False, "error": action_plan["error"]}
        
        tool_calls = action_plan.get("tool_calls", [])
        if not tool_calls:
            # 如果没有工具调用，尝试基本的调试步骤
            return await self._execute_default_action()
        
        results = []
        overall_success = True
        
        for tool_call_data in tool_calls:
            try:
                # 创建工具调用对象
                tool_call = ToolCall(
                    tool_name=tool_call_data.get("function", tool_call_data.get("tool_name")),
                    arguments=self._parse_tool_arguments(tool_call_data.get("arguments", ""))
                )
                
                # 执行工具
                result = await self.tool_manager.execute_tool(tool_call)
                results.append(result)
                
                # 记录行动
                self.memory_manager.add_action_memory(
                    f"执行工具: {tool_call.tool_name}",
                    result.result,
                    success=result.success
                )
                
                if not result.success:
                    overall_success = False
                    logger.warning(f"工具执行失败: {result.error}")
                
            except Exception as e:
                logger.error(f"工具调用失败: {e}")
                overall_success = False
                results.append(ToolResult(success=False, result=None, error=str(e)))
        
        return {
            "success": overall_success,
            "results": [r.__dict__ for r in results],
            "tool_calls_count": len(tool_calls)
        }
    
    async def _execute_default_action(self) -> Dict[str, Any]:
        """执行默认调试动作"""
        try:
            # 获取当前状态
            debug_state = await self.gdb_controller.get_debug_state()
            
            if debug_state and debug_state.is_stopped:
                # 如果程序停止，尝试单步执行
                success = await self.gdb_controller.step_over()
                action = "单步执行"
            else:
                # 如果程序运行中，尝试继续执行
                success = await self.gdb_controller.continue_execution()
                action = "继续执行"
            
            self.memory_manager.add_action_memory(
                f"默认动作: {action}",
                {"success": success},
                success=success
            )
            
            return {"success": success, "action": action}
            
        except Exception as e:
            logger.error(f"默认动作执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _create_state_analysis_prompt(self, debug_state) -> str:
        """创建状态分析提示"""
        prompt_parts = [
            "请分析当前的调试状态：",
            ""
        ]
        
        if debug_state:
            prompt_parts.extend([
                f"程序状态: {'运行中' if debug_state.is_running else '已停止'}",
                f"停止原因: {debug_state.stop_reason}",
            ])
            
            if debug_state.current_frame:
                frame = debug_state.current_frame
                prompt_parts.extend([
                    f"当前函数: {frame.function}",
                    f"文件位置: {frame.file}:{frame.line}",
                ])
            
            if debug_state.variables:
                prompt_parts.append("局部变量:")
                for name, var in list(debug_state.variables.items())[:5]:  # 限制显示数量
                    prompt_parts.append(f"  {name} = {var.value}")
        
        prompt_parts.extend([
            "",
            "请回答以下问题：",
            "1. 当前状态是否表明存在问题？",
            "2. 如果存在问题，可能的原因是什么？",
            "3. 问题是否已经解决？",
            "4. 下一步应该采取什么行动？"
        ])
        
        return "\n".join(prompt_parts)
    
    def _create_action_planning_prompt(self, debug_state, analysis_result: Dict[str, Any]) -> str:
        """创建行动规划提示"""
        prompt_parts = [
            "基于当前分析结果，请规划下一步的调试行动：",
            "",
            f"分析结果: {analysis_result.get('analysis', '无')}",
            "",
            "可用的调试工具包括：",
            "- set_breakpoint: 设置断点",
            "- step_over: 单步执行（跳过函数）",
            "- step_into: 单步执行（进入函数）",
            "- continue_execution: 继续执行",
            "- get_variables: 获取变量信息",
            "- evaluate_expression: 计算表达式",
            "- search_code: 搜索相关代码",
            "",
            "请选择最合适的工具并说明理由。"
        ]
        
        return "\n".join(prompt_parts)
    
    def _parse_analysis_response(self, analysis: str) -> Dict[str, Any]:
        """解析分析响应"""
        result = {"analysis": analysis}
        
        # 简单的关键词检测
        analysis_lower = analysis.lower()
        
        if any(word in analysis_lower for word in ["解决", "修复", "完成", "正常"]):
            result["solved"] = True
            result["solution"] = analysis
        else:
            result["solved"] = False
        
        if any(word in analysis_lower for word in ["错误", "异常", "崩溃", "段错误"]):
            result["has_error"] = True
        
        return result
    
    def _parse_tool_arguments(self, arguments_str: str) -> Dict[str, Any]:
        """解析工具参数"""
        try:
            # 尝试解析JSON
            import json
            return json.loads(arguments_str)
        except:
            # 如果不是JSON，尝试简单解析
            if "=" in arguments_str:
                args = {}
                for part in arguments_str.split(","):
                    if "=" in part:
                        key, value = part.split("=", 1)
                        args[key.strip()] = value.strip().strip('"\'')
                return args
            else:
                # 单个参数
                return {"value": arguments_str.strip().strip('"\'')}
