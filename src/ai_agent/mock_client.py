import json
import random
import re
import os
import time
from typing import List, Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from loguru import logger

from ..utils.debug_logger import DebugLogger

@dataclass
class Message:
    """消息"""
    role: str  # system, user, assistant
    content: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class LLMResponse:
    """LLM响应"""
    content: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
class MockClient:
    def __init__(self, config):
        """初始化Mock客户端"""
        self.config = config
        self.client = "mock"
        self.mock_rules = self._load_mock_rules()
        logger.info("Mock LLM客户端初始化成功")
    

    def _load_mock_rules(self) -> Dict[str, Any]:
        """加载Mock规则"""
        # 尝试加载外部配置文件
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "mock_rules.json")

        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    rules = json.load(f)
                logger.info(f"已加载Mock规则配置: {config_path}")
                return rules
        except Exception as e:
            logger.warning(f"加载Mock规则配置失败: {e}，使用默认配置")

        # 默认配置
        return {
            "default_responses": [
                "根据错误信息，这可能是一个空指针解引用问题。建议检查相关变量的值。",
                "这个错误通常发生在数组越界访问时。建议检查数组索引是否在有效范围内。",
                "看起来是内存管理问题。建议检查malloc/free的配对使用。",
                "这可能是栈溢出导致的。建议检查递归调用或大型局部变量。"
            ],
            "tool_responses": {
                "set_breakpoint": ["已在指定位置设置断点。"],
                "step_over": ["执行下一行代码。"],
                "step_into": ["进入函数内部。"],
                "continue_execution": ["继续执行程序。"],
                "evaluate_expression": ["表达式求值完成。"],
                "search_code": ["代码搜索完成，找到相关代码片段。"]
            },
            "patterns": {
                "crash": {
                    "keywords": ["段错误", "segmentation fault", "core dump", "崩溃"],
                    "responses": ["检测到程序崩溃问题。建议检查空指针解引用、数组越界等问题。"]
                },
                "memory": {
                    "keywords": ["内存", "memory", "malloc", "free", "leak"],
                    "responses": ["这是内存相关问题。建议检查内存分配和释放。"]
                },
                "pointer": {
                    "keywords": ["指针", "pointer", "null", "空指针"],
                    "responses": ["指针问题分析：检查指针初始化和有效性。"]
                },
                "array": {
                    "keywords": ["数组", "array", "越界", "bounds"],
                    "responses": ["数组访问问题：检查索引范围和边界条件。"]
                }
            },
            "debugging_suggestions": [
                "set_breakpoint('main.c', 25) - 在main.c第25行设置断点",
                "evaluate_expression('ptr') - 检查ptr变量的值",
                "step_over() - 单步执行下一行",
                "search_code('malloc') - 搜索内存分配相关代码"
            ]
        }    

    async def chat_completion(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> LLMResponse:
        """Mock聊天完成"""
        # 获取最后一条用户消息
        user_message = ""
        for msg in reversed(messages):
            if msg.role == "user":
                user_message = msg.content.lower()
                break

        # 生成Mock响应
        response_content = self.generate_response(user_message, tools)

        # 模拟使用统计
        usage = {
            "prompt_tokens": len(user_message.split()),
            "completion_tokens": len(response_content.split()),
            "total_tokens": len(user_message.split()) + len(response_content.split())
        }

        return LLMResponse(
            content=response_content,
            usage=usage,
            metadata={"provider": "mock"}
        )

    def generate_response(self, user_message: str, tools: Optional[List[Dict[str, Any]]] = None) -> str:
        """生成Mock响应"""
        # 检查是否包含特定模式
        response_parts = []

        # 基于关键词生成响应
        patterns = self.mock_rules.get("patterns", {})
        for pattern_type, pattern_config in patterns.items():
            keywords = pattern_config.get("keywords", [])
            if any(keyword in user_message for keyword in keywords):
                responses = pattern_config.get("responses", [])
                if responses:
                    response_parts.append(random.choice(responses))
                    break

        # 如果没有匹配到特定模式，使用默认响应
        if not response_parts:
            default_responses = self.mock_rules.get("default_responses", [])
            if default_responses:
                response_parts.append(random.choice(default_responses))
            else:
                response_parts.append("程序出现了问题，建议进行详细的调试分析。")

        # 添加工具调用建议
        if tools:
            response_parts.append("\n建议的调试步骤：")
            response_parts.append("""
                                  <tool_call>\n{"name": "set_breakpoint", "arguments": {"location": "/ddrive/github/gdb-test/src/config/config_manager.cpp:30"}}\n</tool_call>\n<tool_call>\n{"name": "set_breakpoint", "arguments": {"location": "/ddrive/github/gdb-test/src/main.cpp:83"}}\n</tool_call>\n
                                  """)
            # suggestions = self.mock_rules.get("debugging_suggestions", [
            #     "set_breakpoint('main.c', 25) - 在main.c第25行设置断点",
            #     "evaluate_expression('ptr') - 检查ptr变量的值",
            #     "step_over() - 单步执行下一行",
            #     "search_code('malloc') - 搜索内存分配相关代码"
            # ])
            # # 随机选择1-2个建议
            # selected_suggestions = random.sample(suggestions, min(2, len(suggestions)))
            # response_parts.extend(selected_suggestions)

        return "\n".join(response_parts)
    