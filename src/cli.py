"""
AI-GDB 命令行接口
"""

import argparse
import asyncio
import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from .config import Config
from .ai_agent import AIAgent
from .gdb_controller import GDBController
from .knowledge_base import KnowledgeBase
from .web_ui import WebUI


def setup_logging(config: Config) -> None:
    """设置日志配置"""
    logger.remove()
    
    # 控制台日志
    if config.enable_console_log:
        logger.add(
            sys.stderr,
            level=config.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
    
    # 文件日志
    if config.log_file_path:
        log_path = Path(config.log_file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        logger.add(
            config.log_file_path,
            level=config.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days"
        )


async def main_async(args: argparse.Namespace) -> None:
    """异步主函数"""
    try:
        # 加载配置
        config = Config.from_env(args.config)
        setup_logging(config)
        
        logger.info("AI-GDB 自动化调试工具启动")
        logger.info(f"配置文件: {args.config}")
        logger.info(f"项目目录: {config.project_code_dir}")
        logger.info(f"目标可执行文件: {config.target_executable}")
        
        # 初始化组件
        knowledge_base = KnowledgeBase(config)
        gdb_controller = GDBController(config)
        ai_agent = AIAgent(config, knowledge_base, gdb_controller)
        
        if args.command == "build-kb":
            # 构建知识库
            logger.info("开始构建代码知识库...")
            await knowledge_base.build_knowledge_base()
            logger.info("代码知识库构建完成")
            
        elif args.command == "debug":
            # 开始调试
            logger.info("开始自动化调试...")
            if args.test_script:
                config.test_script_path = args.test_script
            
            result = await ai_agent.start_debug_session(
                fault_description=args.fault_description
            )
            
            logger.info("调试会话完成")
            logger.info(f"调试结果: {result}")
            
        elif args.command == "web":
            # 启动Web界面
            logger.info("启动Web界面...")
            web_ui = WebUI(config, ai_agent)
            await web_ui.start()
            
        else:
            logger.error(f"未知命令: {args.command}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


def main() -> None:
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AI-GDB 自动化调试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 构建代码知识库
  ai-gdb build-kb --config .env
  
  # 开始自动化调试
  ai-gdb debug --config .env --fault-description "程序在malloc时发生段错误"
  
  # 启动Web界面
  ai-gdb web --config .env
        """
    )
    
    parser.add_argument(
        "command",
        choices=["build-kb", "debug", "web"],
        help="要执行的命令"
    )
    
    parser.add_argument(
        "--config",
        default=".env",
        help="配置文件路径 (默认: .env)"
    )
    
    parser.add_argument(
        "--fault-description",
        help="故障描述 (用于debug命令)"
    )
    
    parser.add_argument(
        "--test-script",
        help="测试脚本路径 (用于debug命令)"
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if args.command == "debug" and not args.fault_description:
        parser.error("debug命令需要提供 --fault-description 参数")
    
    # 运行异步主函数
    asyncio.run(main_async(args))


if __name__ == "__main__":
    main()
