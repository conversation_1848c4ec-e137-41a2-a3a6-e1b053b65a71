"""
断点管理器
"""

import re
import asyncio
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class Breakpoint:
    """断点定义"""
    id: Optional[int]
    location: str
    condition: Optional[str] = None
    enabled: bool = True
    temporary: bool = False
    hit_count: int = 0
    ignore_count: int = 0
    commands: List[str] = None
    
    def __post_init__(self):
        if self.commands is None:
            self.commands = []


class BreakpointManager:
    """断点管理器"""
    
    def __init__(self, gdb_session):
        self.gdb_session = gdb_session
        self.breakpoints: Dict[int, Breakpoint] = {}
        self.next_id = 1
        self._location_to_id: Dict[str, int] = {}
    
    # async def _check_program_state(self) -> str:
    #     """检查程序运行状态"""
    #     try:
    #         output = await self.gdb_session.execute_command("info program")
    #         if "not being run" in output:
    #             return "not_running"
    #         elif "stopped" in output:
    #             return "stopped"
    #         elif "running" in output:
    #             return "running"
    #         else:
    #             return "unknown"
    #     except Exception as e:
    #         logger.debug(f"检查程序状态失败: {e}")
    #         return "unknown"

    async def set_breakpoint(
        self,
        location: str,
        condition: Optional[str] = None,
        temporary: bool = False,
        commands: List[str] = None,
        max_retries: int = 3
    ) -> Optional[int]:
        """设置断点"""
        try:
            # 检查是否已存在相同位置的断点
            if location in self._location_to_id:
                existing_id = self._location_to_id[location]
                logger.warning(f"断点已存在于位置 {location}, ID: {existing_id}")
                return existing_id

            # 构建断点命令
            if temporary:
                cmd = f"tbreak {location}"
            else:
                cmd = f"break {location}"

            # 添加条件
            if condition:
                cmd += f" if {condition}"

            logger.info(f"设置断点: {cmd}")

            # 重试机制
            for attempt in range(max_retries):
                try:
                    # 执行断点命令
                    output = await self.gdb_session.execute_command(cmd)
                    logger.debug(f"断点设置命令输出 (尝试 {attempt + 1}): {repr(output)}")

                    # 解析断点ID
                    bp_id = self._parse_breakpoint_id(output)
                    if bp_id is not None:
                        # 创建断点对象
                        breakpoint = Breakpoint(
                            id=bp_id,
                            location=location,
                            condition=condition,
                            temporary=temporary,
                            commands=commands or []
                        )

                        # 保存断点
                        self.breakpoints[bp_id] = breakpoint
                        self._location_to_id[location] = bp_id

                        # 设置断点命令
                        if commands:
                            await self._set_breakpoint_commands(bp_id, commands)

                        logger.info(f"断点设置成功: ID={bp_id}, 位置={location}")
                        return bp_id

                    # 如果解析失败，检查是否是信号处理输出干扰
                    if "handle SIGSEGV" in output or "Signal        Stop" in output:
                        logger.warning(f"检测到信号处理输出干扰，等待后重试 (尝试 {attempt + 1}/{max_retries})")
                        await asyncio.sleep(0.5)  # 短暂等待
                        continue

                    # 检查是否是其他错误
                    if "No symbol table" in output:
                        logger.error(f"符号表未加载，无法设置断点: {location}")
                        return None
                    elif "No line" in output:
                        logger.error(f"指定行不存在，无法设置断点: {location}")
                        return None
                    elif "Function" in output and "not defined" in output:
                        logger.error(f"函数未定义，无法设置断点: {location}")
                        return None
                    elif "No source file named" in output:
                        logger.error(f"源文件不存在，无法设置断点: {location}")
                        return None
                    elif "The program is not being run" in output:
                        logger.warning(f"程序未运行，跳过此次尝试 (尝试 {attempt + 1}/{max_retries})")
                        await asyncio.sleep(0.5)
                        continue
                    elif "^error" in output:
                        logger.warning(f"GDB命令执行错误，尝试 {attempt + 1}/{max_retries}: {output}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)
                            continue

                    logger.warning(f"断点设置失败，尝试 {attempt + 1}/{max_retries}: {output}")

                except Exception as e:
                    logger.error(f"断点设置命令执行失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(0.5)

            logger.error(f"经过 {max_retries} 次尝试，断点设置失败: {location}")
            return None

        except Exception as e:
            logger.error(f"设置断点失败: {e}")
            return None
    
    async def set_function_breakpoint(self, function_name: str, **kwargs) -> Optional[int]:
        """在函数入口设置断点"""
        return await self.set_breakpoint(function_name, **kwargs)
    
    async def set_line_breakpoint(self, file_path: str, line_number: int, **kwargs) -> Optional[int]:
        """在指定行设置断点"""
        location = f"{file_path}:{line_number}"
        return await self.set_breakpoint(location, **kwargs)
    
    async def set_address_breakpoint(self, address: str, **kwargs) -> Optional[int]:
        """在指定地址设置断点"""
        location = f"*{address}"
        return await self.set_breakpoint(location, **kwargs)
    
    async def set_watchpoint(self, expression: str, watch_type: str = "write") -> Optional[int]:
        """设置观察点"""
        try:
            # 构建观察点命令
            if watch_type == "write":
                cmd = f"watch {expression}"
            elif watch_type == "read":
                cmd = f"rwatch {expression}"
            elif watch_type == "access":
                cmd = f"awatch {expression}"
            else:
                logger.error(f"不支持的观察点类型: {watch_type}")
                return None
            
            logger.info(f"设置观察点: {cmd}")
            
            # 执行命令
            output = await self.gdb_session.execute_command(cmd)
            
            # 解析观察点ID
            wp_id = self._parse_breakpoint_id(output)
            if wp_id is None:
                logger.error(f"设置观察点失败: {output}")
                return None
            
            # 创建观察点对象
            watchpoint = Breakpoint(
                id=wp_id,
                location=expression,
                condition=None,
                temporary=False
            )
            
            self.breakpoints[wp_id] = watchpoint
            
            logger.info(f"观察点设置成功: ID={wp_id}, 表达式={expression}")
            return wp_id
            
        except Exception as e:
            logger.error(f"设置观察点失败: {e}")
            return None
    
    async def remove_breakpoint(self, bp_id: int) -> bool:
        """删除断点"""
        try:
            if bp_id not in self.breakpoints:
                logger.warning(f"断点不存在: ID={bp_id}")
                return False
            
            logger.info(f"删除断点: ID={bp_id}")
            
            # 执行删除命令
            output = await self.gdb_session.execute_command(f"delete {bp_id}")
            
            # 从管理器中移除
            breakpoint = self.breakpoints.pop(bp_id)
            if breakpoint.location in self._location_to_id:
                del self._location_to_id[breakpoint.location]
            
            logger.info(f"断点删除成功: ID={bp_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除断点失败: {e}")
            return False
    
    async def enable_breakpoint(self, bp_id: int) -> bool:
        """启用断点"""
        try:
            if bp_id not in self.breakpoints:
                logger.warning(f"断点不存在: ID={bp_id}")
                return False
            
            logger.info(f"启用断点: ID={bp_id}")
            
            # 执行启用命令
            output = await self.gdb_session.execute_command(f"enable {bp_id}")
            
            # 更新状态
            self.breakpoints[bp_id].enabled = True
            
            logger.info(f"断点启用成功: ID={bp_id}")
            return True
            
        except Exception as e:
            logger.error(f"启用断点失败: {e}")
            return False
    
    async def disable_breakpoint(self, bp_id: int) -> bool:
        """禁用断点"""
        try:
            if bp_id not in self.breakpoints:
                logger.warning(f"断点不存在: ID={bp_id}")
                return False
            
            logger.info(f"禁用断点: ID={bp_id}")
            
            # 执行禁用命令
            output = await self.gdb_session.execute_command(f"disable {bp_id}")
            
            # 更新状态
            self.breakpoints[bp_id].enabled = False
            
            logger.info(f"断点禁用成功: ID={bp_id}")
            return True
            
        except Exception as e:
            logger.error(f"禁用断点失败: {e}")
            return False
    
    async def modify_breakpoint_condition(self, bp_id: int, condition: str) -> bool:
        """修改断点条件"""
        try:
            if bp_id not in self.breakpoints:
                logger.warning(f"断点不存在: ID={bp_id}")
                return False
            
            logger.info(f"修改断点条件: ID={bp_id}, 条件={condition}")
            
            # 执行条件命令
            output = await self.gdb_session.execute_command(f"condition {bp_id} {condition}")
            
            # 更新断点对象
            self.breakpoints[bp_id].condition = condition
            
            logger.info(f"断点条件修改成功: ID={bp_id}")
            return True
            
        except Exception as e:
            logger.error(f"修改断点条件失败: {e}")
            return False
    
    async def set_breakpoint_ignore_count(self, bp_id: int, count: int) -> bool:
        """设置断点忽略次数"""
        try:
            if bp_id not in self.breakpoints:
                logger.warning(f"断点不存在: ID={bp_id}")
                return False
            
            logger.info(f"设置断点忽略次数: ID={bp_id}, 次数={count}")
            
            # 执行忽略命令
            output = await self.gdb_session.execute_command(f"ignore {bp_id} {count}")
            
            # 更新断点对象
            self.breakpoints[bp_id].ignore_count = count
            
            logger.info(f"断点忽略次数设置成功: ID={bp_id}")
            return True
            
        except Exception as e:
            logger.error(f"设置断点忽略次数失败: {e}")
            return False
    
    async def _set_breakpoint_commands(self, bp_id: int, commands: List[str]) -> bool:
        """设置断点命令"""
        try:
            logger.info(f"设置断点命令: ID={bp_id}")
            
            # 开始命令设置
            await self.gdb_session.execute_command(f"commands {bp_id}")
            
            # 发送每个命令
            for cmd in commands:
                await self.gdb_session.execute_command(cmd)
            
            # 结束命令设置
            await self.gdb_session.execute_command("end")
            
            logger.info(f"断点命令设置成功: ID={bp_id}")
            return True
            
        except Exception as e:
            logger.error(f"设置断点命令失败: {e}")
            return False
    
    async def list_breakpoints(self) -> List[Breakpoint]:
        """列出所有断点"""
        try:
            # 获取最新的断点信息
            output = await self.gdb_session.execute_command("info breakpoints")
            
            # 更新断点状态
            self._update_breakpoints_from_output(output)
            
            return list(self.breakpoints.values())
            
        except Exception as e:
            logger.error(f"列出断点失败: {e}")
            return []
    
    async def clear_all_breakpoints(self) -> bool:
        """清除所有断点"""
        try:
            logger.info("清除所有断点")
            
            # 执行清除命令
            output = await self.gdb_session.execute_command("delete")
            
            # 清空管理器
            self.breakpoints.clear()
            self._location_to_id.clear()
            
            logger.info("所有断点已清除")
            return True
            
        except Exception as e:
            logger.error(f"清除断点失败: {e}")
            return False
    
    def _parse_breakpoint_id(self, output: str) -> Optional[int]:
        """从GDB输出中解析断点ID"""
        try:
            logger.debug(f"解析断点ID，输出内容: {repr(output)}")

            # 检查是否包含信号处理输出（这不是断点设置的结果）
            if "handle SIGSEGV" in output or "Signal        Stop" in output:
                logger.warning("检测到信号处理输出，这不是断点设置的结果")
                return None

            # 查找断点ID模式
            patterns = [
                r'Breakpoint (\d+) at',
                r'Hardware watchpoint (\d+):',
                r'Watchpoint (\d+):',
                r'Temporary breakpoint (\d+) at',
                # 添加更多可能的模式
                r'Breakpoint (\d+):'
            ]

            for pattern in patterns:
                match = re.search(pattern, output)
                if match:
                    bp_id = int(match.group(1))
                    logger.debug(f"成功解析断点ID: {bp_id}")
                    return bp_id

            # 如果没有找到断点ID，记录详细信息
            logger.warning(f"未能从输出中解析断点ID: {repr(output[:200])}")
            return None

        except Exception as e:
            logger.error(f"解析断点ID时发生异常: {e}")
            return None
    
    def _update_breakpoints_from_output(self, output: str):
        """从GDB输出更新断点信息"""
        try:
            lines = output.split('\n')
            
            for line in lines:
                if line.strip() and not line.startswith('Num'):
                    parts = line.split()
                    if len(parts) >= 4:
                        try:
                            bp_id = int(parts[0])
                            enabled = parts[3] == 'y'
                            
                            if bp_id in self.breakpoints:
                                self.breakpoints[bp_id].enabled = enabled
                                
                                # 尝试解析命中次数
                                if 'breakpoint already hit' in line:
                                    hit_match = re.search(r'(\d+) times?', line)
                                    if hit_match:
                                        self.breakpoints[bp_id].hit_count = int(hit_match.group(1))
                                        
                        except (ValueError, IndexError):
                            continue
                            
        except Exception as e:
            logger.error(f"更新断点信息失败: {e}")
    
    def get_breakpoint(self, bp_id: int) -> Optional[Breakpoint]:
        """获取指定断点"""
        return self.breakpoints.get(bp_id)
    
    def get_breakpoint_by_location(self, location: str) -> Optional[Breakpoint]:
        """根据位置获取断点"""
        bp_id = self._location_to_id.get(location)
        if bp_id:
            return self.breakpoints.get(bp_id)
        return None
    
    def get_breakpoints_count(self) -> int:
        """获取断点数量"""
        return len(self.breakpoints)
    
    def get_enabled_breakpoints(self) -> List[Breakpoint]:
        """获取启用的断点"""
        return [bp for bp in self.breakpoints.values() if bp.enabled]
    
    def get_disabled_breakpoints(self) -> List[Breakpoint]:
        """获取禁用的断点"""
        return [bp for bp in self.breakpoints.values() if not bp.enabled]
