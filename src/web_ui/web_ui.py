"""
Web界面主模块
"""

import asyncio
from pathlib import Path
from typing import Optional
from loguru import logger

try:
    from fastapi import FastAPI, Request
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jinja2Templates
    from fastapi.responses import HTMLResponse
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logger.warning("FastAPI不可用，Web界面将无法启动")

from .api_routes import create_api_routes
from .websocket_handler import WebSocketHandler


class WebUI:
    """Web界面管理器"""
    
    def __init__(self, config, ai_agent):
        self.config = config
        self.ai_agent = ai_agent
        self.app: Optional[FastAPI] = None
        self.websocket_handler: Optional[WebSocketHandler] = None
        
        if FASTAPI_AVAILABLE:
            self._init_app()
        else:
            logger.error("FastAPI不可用，无法初始化Web界面")
    
    def _init_app(self):
        """初始化FastAPI应用"""
        self.app = FastAPI(
            title="AI-GDB 自动化调试工具",
            description="基于AI的C/C++自动化调试工具",
            version="0.1.0"
        )
        
        # 设置模板目录
        template_dir = Path(__file__).parent / "templates"
        template_dir.mkdir(exist_ok=True)
        self.templates = Jinja2Templates(directory=str(template_dir))
        
        # 设置静态文件目录
        static_dir = Path(__file__).parent / "static"
        static_dir.mkdir(exist_ok=True)
        self.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        
        # 初始化WebSocket处理器
        self.websocket_handler = WebSocketHandler(self.ai_agent)
        
        # 注册路由
        self._register_routes()
        
        logger.info("Web界面初始化完成")
    
    def _register_routes(self):
        """注册路由"""
        # 主页
        @self.app.get("/", response_class=HTMLResponse)
        async def home(request: Request):
            return self.templates.TemplateResponse(
                "index.html",
                {
                    "request": request,
                    "title": "AI-GDB 自动化调试工具"
                }
            )
        
        # 调试页面
        @self.app.get("/debug", response_class=HTMLResponse)
        async def debug_page(request: Request):
            return self.templates.TemplateResponse(
                "debug.html",
                {
                    "request": request,
                    "title": "调试界面"
                }
            )
        
        # 知识库页面
        @self.app.get("/knowledge", response_class=HTMLResponse)
        async def knowledge_page(request: Request):
            return self.templates.TemplateResponse(
                "knowledge.html",
                {
                    "request": request,
                    "title": "代码知识库"
                }
            )
        
        # 历史记录页面
        @self.app.get("/history", response_class=HTMLResponse)
        async def history_page(request: Request):
            return self.templates.TemplateResponse(
                "history.html",
                {
                    "request": request,
                    "title": "调试历史"
                }
            )
        
        # 注册API路由
        api_router = create_api_routes(self.ai_agent)
        self.app.include_router(api_router, prefix="/api")
        
        # 注册WebSocket路由
        self.app.websocket("/ws")(self.websocket_handler.websocket_endpoint)
    
    async def start(self):
        """启动Web服务器"""
        if not FASTAPI_AVAILABLE or not self.app:
            logger.error("无法启动Web界面：FastAPI不可用")
            return
        
        try:
            logger.info(f"启动Web界面: http://{self.config.web_host}:{self.config.web_port}")
            
            config = uvicorn.Config(
                app=self.app,
                host=self.config.web_host,
                port=self.config.web_port,
                log_level="info"
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"Web界面启动失败: {e}")
            raise
    
    def get_app(self) -> Optional[FastAPI]:
        """获取FastAPI应用实例"""
        return self.app
