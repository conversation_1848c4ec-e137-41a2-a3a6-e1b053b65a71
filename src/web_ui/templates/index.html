{% extends "base.html" %}

{% block title %}AI-GDB 自动化调试工具 - 首页{% endblock %}

{% block content %}
<div class="row">
    <!-- 欢迎区域 -->
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">
                <i class="fas fa-robot"></i> AI-GDB 自动化调试工具
            </h1>
            <p class="lead">
                借助大模型+AI Agent能力，实现完全自动化的GDB单步调试能力，解决C/C++项目代码的故障。
            </p>
            <hr class="my-4">
            <p>
                支持智能代码分析、自动断点设置、单步调试、变量监控等功能。
            </p>
            <a class="btn btn-light btn-lg" href="/debug" role="button">
                <i class="fas fa-play"></i> 开始调试
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- 系统状态 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 系统状态</h5>
            </div>
            <div class="card-body">
                <div id="system-status">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 知识库统计 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-database"></i> 知识库统计</h5>
            </div>
            <div class="card-body">
                <div id="knowledge-stats">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 功能特性 -->
    <div class="col-12">
        <h3><i class="fas fa-star"></i> 主要功能</h3>
        <div class="row">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-brain fa-3x text-primary mb-3"></i>
                        <h5>智能分析</h5>
                        <p class="card-text">
                            基于大语言模型的智能代码分析，自动识别潜在问题和调试策略。
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-crosshairs fa-3x text-success mb-3"></i>
                        <h5>自动断点</h5>
                        <p class="card-text">
                            智能设置断点位置，自动捕获关键执行点和异常情况。
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-3x text-info mb-3"></i>
                        <h5>实时监控</h5>
                        <p class="card-text">
                            实时监控程序执行状态、变量变化和内存使用情况。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 最近调试会话 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history"></i> 最近调试会话</h5>
                <a href="/history" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                <div id="recent-sessions">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    // 加载系统状态
    loadSystemStatus();
    
    // 加载知识库统计
    loadKnowledgeStats();
    
    // 加载最近会话
    loadRecentSessions();
    
    // 定期刷新状态
    setInterval(loadSystemStatus, 5000);
});

function loadSystemStatus() {
    $.get('/api/status')
        .done(function(response) {
            if (response.success) {
                renderSystemStatus(response.data);
            } else {
                $('#system-status').html('<div class="alert alert-danger">加载失败: ' + response.error + '</div>');
            }
        })
        .fail(function() {
            $('#system-status').html('<div class="alert alert-danger">网络错误</div>');
        });
}

function renderSystemStatus(data) {
    const html = `
        <div class="row">
            <div class="col-6">
                <strong>调试状态:</strong><br>
                <span class="badge ${data.is_debugging ? 'bg-success' : 'bg-secondary'}">
                    ${data.is_debugging ? '调试中' : '空闲'}
                </span>
            </div>
            <div class="col-6">
                <strong>调试轮次:</strong><br>
                ${data.current_round} / ${data.max_rounds}
            </div>
        </div>
        ${data.current_session ? `
        <hr>
        <div class="mt-2">
            <strong>当前会话:</strong><br>
            <small class="text-muted">${data.current_session.error_description}</small>
        </div>
        ` : ''}
    `;
    $('#system-status').html(html);
}

function loadKnowledgeStats() {
    $.get('/api/knowledge/stats')
        .done(function(response) {
            if (response.success) {
                renderKnowledgeStats(response.data);
            } else {
                $('#knowledge-stats').html('<div class="alert alert-danger">加载失败: ' + response.error + '</div>');
            }
        })
        .fail(function() {
            $('#knowledge-stats').html('<div class="alert alert-danger">网络错误</div>');
        });
}

function renderKnowledgeStats(data) {
    const html = `
        <div class="row">
            <div class="col-6">
                <strong>代码块:</strong><br>
                <span class="h4 text-primary">${data.total_chunks}</span>
            </div>
            <div class="col-6">
                <strong>符号:</strong><br>
                <span class="h4 text-success">${data.total_symbols}</span>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-6">
                <strong>文件数:</strong><br>
                ${data.file_count}
            </div>
            <div class="col-6">
                <strong>语言:</strong><br>
                ${data.languages.join(', ')}
            </div>
        </div>
    `;
    $('#knowledge-stats').html(html);
}

function loadRecentSessions() {
    $.get('/api/memory/sessions')
        .done(function(response) {
            if (response.success) {
                renderRecentSessions(response.data.slice(0, 5)); // 只显示前5个
            } else {
                $('#recent-sessions').html('<div class="alert alert-danger">加载失败: ' + response.error + '</div>');
            }
        })
        .fail(function() {
            $('#recent-sessions').html('<div class="alert alert-danger">网络错误</div>');
        });
}

function renderRecentSessions(sessions) {
    if (sessions.length === 0) {
        $('#recent-sessions').html('<p class="text-muted">暂无调试会话</p>');
        return;
    }
    
    let html = '<div class="list-group">';
    sessions.forEach(function(session) {
        const statusBadge = session.status === 'completed' ? 'bg-success' : 
                           session.status === 'failed' ? 'bg-danger' : 'bg-warning';
        const duration = Math.round(session.duration / 60); // 转换为分钟
        
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${session.session_id}</h6>
                    <small>
                        <span class="badge ${statusBadge}">${session.status}</span>
                        ${duration}分钟
                    </small>
                </div>
                <p class="mb-1">${session.error_description}</p>
                <small class="text-muted">内存: ${session.memory_count} 条</small>
            </div>
        `;
    });
    html += '</div>';
    
    $('#recent-sessions').html(html);
}
</script>
{% endblock %}
