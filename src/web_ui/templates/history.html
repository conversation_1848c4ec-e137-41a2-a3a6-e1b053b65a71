{% extends "base.html" %}

{% block title %}AI-GDB 调试历史{% endblock %}

{% block content %}
<div class="row">
    <!-- 历史统计 -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> 调试历史统计</h5>
            </div>
            <div class="card-body">
                <div class="row" id="history-stats">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 筛选和搜索 -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> 筛选条件</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="status-filter" class="form-label">状态</label>
                    <select class="form-select" id="status-filter">
                        <option value="">全部状态</option>
                        <option value="completed">已完成</option>
                        <option value="failed">失败</option>
                        <option value="cancelled">已取消</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="date-range" class="form-label">时间范围</label>
                    <select class="form-select" id="date-range">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                
                <div class="mb-3" id="custom-date-range" style="display: none;">
                    <label for="start-date" class="form-label">开始日期</label>
                    <input type="date" class="form-control mb-2" id="start-date">
                    <label for="end-date" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="end-date">
                </div>
                
                <div class="mb-3">
                    <label for="search-text" class="form-label">搜索</label>
                    <input type="text" class="form-control" id="search-text" 
                           placeholder="搜索错误描述...">
                </div>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" id="apply-filter-btn">
                        <i class="fas fa-search"></i> 应用筛选
                    </button>
                    <button type="button" class="btn btn-secondary" id="reset-filter-btn">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> 快速操作</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info" id="export-history-btn">
                        <i class="fas fa-download"></i> 导出历史
                    </button>
                    <button type="button" class="btn btn-outline-warning" id="clear-old-btn">
                        <i class="fas fa-broom"></i> 清理旧记录
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="clear-all-btn">
                        <i class="fas fa-trash"></i> 清空全部
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 历史记录列表 -->
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history"></i> 调试会话历史</h5>
                <div>
                    <span id="session-count" class="badge bg-secondary me-2">0 个会话</span>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="refresh-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="session-list">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
                
                <!-- 分页 -->
                <nav id="pagination-nav" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 会话详情模态框 -->
<div class="modal fade" id="session-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 
                    <span id="modal-session-id">会话详情</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>会话ID:</strong></td>
                                <td id="detail-session-id"></td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td id="detail-status"></td>
                            </tr>
                            <tr>
                                <td><strong>开始时间:</strong></td>
                                <td id="detail-start-time"></td>
                            </tr>
                            <tr>
                                <td><strong>结束时间:</strong></td>
                                <td id="detail-end-time"></td>
                            </tr>
                            <tr>
                                <td><strong>持续时间:</strong></td>
                                <td id="detail-duration"></td>
                            </tr>
                            <tr>
                                <td><strong>调试轮次:</strong></td>
                                <td id="detail-rounds"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>错误描述</h6>
                        <div class="bg-light p-3 rounded">
                            <p id="detail-error-description"></p>
                        </div>
                        
                        <h6 class="mt-3">解决方案</h6>
                        <div class="bg-light p-3 rounded">
                            <p id="detail-solution"></p>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>调试记忆</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>类型</th>
                                        <th>内容</th>
                                    </tr>
                                </thead>
                                <tbody id="detail-memories">
                                    <!-- 记忆内容将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="replay-session-btn">
                    <i class="fas fa-play"></i> 重放会话
                </button>
                <button type="button" class="btn btn-success" id="continue-session-btn">
                    <i class="fas fa-forward"></i> 继续调试
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导出选项模态框 -->
<div class="modal fade" id="export-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download"></i> 导出历史记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">导出格式</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="export-format" 
                               id="format-json" value="json" checked>
                        <label class="form-check-label" for="format-json">
                            JSON 格式
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="export-format" 
                               id="format-csv" value="csv">
                        <label class="form-check-label" for="format-csv">
                            CSV 格式
                        </label>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">导出内容</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include-memories" checked>
                        <label class="form-check-label" for="include-memories">
                            包含调试记忆
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include-solutions" checked>
                        <label class="form-check-label" for="include-solutions">
                            包含解决方案
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-export-btn">
                    <i class="fas fa-download"></i> 导出
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentPage = 1;
let pageSize = 10;
let totalSessions = 0;
let currentFilters = {};

$(document).ready(function() {
    // 初始化组件
    initializeHistoryComponents();
    
    // 绑定事件处理器
    bindHistoryEventHandlers();
    
    // 加载历史统计
    loadHistoryStats();
    
    // 加载会话列表
    loadSessionList();
});

function initializeHistoryComponents() {
    // 设置默认日期
    const today = new Date().toISOString().split('T')[0];
    $('#end-date').val(today);
    
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    $('#start-date').val(weekAgo.toISOString().split('T')[0]);
}

function bindHistoryEventHandlers() {
    // 日期范围选择
    $('#date-range').change(function() {
        const value = $(this).val();
        if (value === 'custom') {
            $('#custom-date-range').show();
        } else {
            $('#custom-date-range').hide();
        }
    });
    
    // 筛选按钮
    $('#apply-filter-btn').click(applyFilters);
    $('#reset-filter-btn').click(resetFilters);
    
    // 刷新按钮
    $('#refresh-btn').click(() => loadSessionList());
    
    // 快速操作按钮
    $('#export-history-btn').click(() => $('#export-modal').modal('show'));
    $('#clear-old-btn').click(clearOldSessions);
    $('#clear-all-btn').click(clearAllSessions);
    
    // 导出确认
    $('#confirm-export-btn').click(exportHistory);
    
    // 会话操作按钮
    $('#replay-session-btn').click(replaySession);
    $('#continue-session-btn').click(continueSession);
    
    // 搜索框回车
    $('#search-text').keypress(function(e) {
        if (e.which === 13) {
            applyFilters();
        }
    });
}

function loadHistoryStats() {
    AIGDB.utils.showLoading('#history-stats', '加载统计信息...');
    
    $.get('/api/memory/stats')
        .done(function(response) {
            if (response.success) {
                renderHistoryStats(response.data);
            } else {
                AIGDB.utils.showError('#history-stats', response.error);
            }
        })
        .fail(function() {
            AIGDB.utils.showError('#history-stats', '加载统计信息失败');
        });
}

function renderHistoryStats(data) {
    const html = `
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-primary">${data.total_sessions || 0}</h3>
                <p class="mb-0">总会话数</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-success">${data.completed_sessions || 0}</h3>
                <p class="mb-0">成功完成</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-danger">${data.failed_sessions || 0}</h3>
                <p class="mb-0">失败会话</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-info">${data.avg_duration ? Math.round(data.avg_duration / 60) : 0}</h3>
                <p class="mb-0">平均时长(分钟)</p>
            </div>
        </div>
    `;
    
    $('#history-stats').html(html);
}

function loadSessionList(page = 1) {
    currentPage = page;
    AIGDB.utils.showLoading('#session-list', '加载会话列表...');
    
    const params = {
        page: page,
        page_size: pageSize,
        ...currentFilters
    };
    
    $.get('/api/memory/sessions', params)
        .done(function(response) {
            if (response.success) {
                renderSessionList(response.data);
                totalSessions = response.total || 0;
                updatePagination();
            } else {
                AIGDB.utils.showError('#session-list', response.error);
            }
        })
        .fail(function() {
            AIGDB.utils.showError('#session-list', '加载会话列表失败');
        });
}

function renderSessionList(sessions) {
    if (!sessions || sessions.length === 0) {
        $('#session-list').html(`
            <div class="text-center text-muted">
                <i class="fas fa-history fa-3x mb-3"></i>
                <p>暂无调试会话记录</p>
            </div>
        `);
        $('#session-count').text('0 个会话');
        return;
    }
    
    let html = '';
    sessions.forEach(session => {
        const statusBadge = getStatusBadge(session.status);
        const duration = Math.round(session.duration / 60);
        const startTime = AIGDB.utils.formatTimestamp(session.start_time);
        
        html += `
            <div class="card mb-3 session-item">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-bug"></i>
                        ${session.session_id}
                    </h6>
                    <div>
                        ${statusBadge}
                        <span class="badge bg-secondary ms-1">${duration}分钟</span>
                    </div>
                </div>
                <div class="card-body">
                    <p class="mb-2">${session.error_description}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> ${startTime}
                            <span class="ms-2">
                                <i class="fas fa-memory"></i> ${session.memory_count} 条记忆
                            </span>
                        </small>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary view-session-btn" 
                                    data-session-id="${session.session_id}">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            <button type="button" class="btn btn-outline-danger delete-session-btn" 
                                    data-session-id="${session.session_id}">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#session-list').html(html);
    $('#session-count').text(`${sessions.length} 个会话`);
    
    // 绑定按钮事件
    $('.view-session-btn').click(function() {
        const sessionId = $(this).data('session-id');
        viewSessionDetail(sessionId);
    });
    
    $('.delete-session-btn').click(function() {
        const sessionId = $(this).data('session-id');
        deleteSession(sessionId);
    });
}

function getStatusBadge(status) {
    const badges = {
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'cancelled': '<span class="badge bg-warning">已取消</span>',
        'running': '<span class="badge bg-info">运行中</span>'
    };
    
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

function updatePagination() {
    const totalPages = Math.ceil(totalSessions / pageSize);
    
    if (totalPages <= 1) {
        $('#pagination-nav').hide();
        return;
    }
    
    $('#pagination-nav').show();
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || 
            (i >= currentPage - 2 && i <= currentPage + 2)) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
        </li>
    `;
    
    $('#pagination').html(html);
    
    // 绑定分页事件
    $('.page-link').click(function(e) {
        e.preventDefault();
        const page = parseInt($(this).data('page'));
        if (page && page !== currentPage) {
            loadSessionList(page);
        }
    });
}

function applyFilters() {
    currentFilters = {};
    
    const status = $('#status-filter').val();
    if (status) currentFilters.status = status;
    
    const dateRange = $('#date-range').val();
    if (dateRange && dateRange !== 'custom') {
        currentFilters.date_range = dateRange;
    } else if (dateRange === 'custom') {
        const startDate = $('#start-date').val();
        const endDate = $('#end-date').val();
        if (startDate) currentFilters.start_date = startDate;
        if (endDate) currentFilters.end_date = endDate;
    }
    
    const searchText = $('#search-text').val().trim();
    if (searchText) currentFilters.search = searchText;
    
    loadSessionList(1);
}

function resetFilters() {
    $('#status-filter').val('');
    $('#date-range').val('');
    $('#search-text').val('');
    $('#custom-date-range').hide();
    
    currentFilters = {};
    loadSessionList(1);
}

function viewSessionDetail(sessionId) {
    $.get(`/api/memory/sessions/${sessionId}`)
        .done(function(response) {
            if (response.success) {
                showSessionDetail(response.data);
            } else {
                AIGDB.utils.showNotification('加载会话详情失败: ' + response.error, 'error');
            }
        })
        .fail(function() {
            AIGDB.utils.showNotification('加载会话详情失败', 'error');
        });
}

function showSessionDetail(session) {
    $('#modal-session-id').text(session.session_id);
    $('#detail-session-id').text(session.session_id);
    $('#detail-status').html(getStatusBadge(session.status));
    $('#detail-start-time').text(AIGDB.utils.formatTimestamp(session.start_time));
    $('#detail-end-time').text(session.end_time ? AIGDB.utils.formatTimestamp(session.end_time) : '未结束');
    $('#detail-duration').text(Math.round(session.duration / 60) + ' 分钟');
    $('#detail-rounds').text(session.debug_rounds || 0);
    $('#detail-error-description').text(session.error_description);
    $('#detail-solution').text(session.solution || '暂无解决方案');
    
    // 填充记忆列表
    let memoriesHtml = '';
    if (session.memories && session.memories.length > 0) {
        session.memories.forEach(memory => {
            memoriesHtml += `
                <tr>
                    <td>${AIGDB.utils.formatTimestamp(memory.timestamp)}</td>
                    <td><span class="badge bg-info">${memory.type}</span></td>
                    <td>${memory.content}</td>
                </tr>
            `;
        });
    } else {
        memoriesHtml = '<tr><td colspan="3" class="text-center text-muted">暂无记忆</td></tr>';
    }
    
    $('#detail-memories').html(memoriesHtml);
    $('#session-detail-modal').modal('show');
}

function deleteSession(sessionId) {
    if (!confirm('确定要删除这个调试会话吗？此操作不可撤销。')) {
        return;
    }
    
    $.ajax({
        url: `/api/memory/sessions/${sessionId}`,
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            AIGDB.utils.showNotification('会话已删除', 'success');
            loadSessionList(currentPage);
            loadHistoryStats();
        } else {
            AIGDB.utils.showNotification('删除会话失败: ' + response.error, 'error');
        }
    })
    .fail(function() {
        AIGDB.utils.showNotification('删除会话失败', 'error');
    });
}

function clearOldSessions() {
    if (!confirm('确定要清理30天前的旧记录吗？此操作不可撤销。')) {
        return;
    }
    
    $.post('/api/memory/cleanup', { days: 30 })
        .done(function(response) {
            if (response.success) {
                AIGDB.utils.showNotification(`已清理 ${response.deleted_count} 条旧记录`, 'success');
                loadSessionList(1);
                loadHistoryStats();
            } else {
                AIGDB.utils.showNotification('清理失败: ' + response.error, 'error');
            }
        })
        .fail(function() {
            AIGDB.utils.showNotification('清理请求失败', 'error');
        });
}

function clearAllSessions() {
    if (!confirm('确定要清空所有调试历史吗？此操作不可撤销。')) {
        return;
    }
    
    $.ajax({
        url: '/api/memory/sessions',
        method: 'DELETE'
    })
    .done(function(response) {
        if (response.success) {
            AIGDB.utils.showNotification('所有历史记录已清空', 'success');
            loadSessionList(1);
            loadHistoryStats();
        } else {
            AIGDB.utils.showNotification('清空失败: ' + response.error, 'error');
        }
    })
    .fail(function() {
        AIGDB.utils.showNotification('清空请求失败', 'error');
    });
}

function exportHistory() {
    const format = $('input[name="export-format"]:checked').val();
    const includeMemories = $('#include-memories').is(':checked');
    const includeSolutions = $('#include-solutions').is(':checked');
    
    const params = new URLSearchParams({
        format: format,
        include_memories: includeMemories,
        include_solutions: includeSolutions,
        ...currentFilters
    });
    
    window.open(`/api/memory/export?${params.toString()}`, '_blank');
    $('#export-modal').modal('hide');
}

function replaySession() {
    const sessionId = $('#detail-session-id').text();
    AIGDB.utils.showNotification('重放功能待实现', 'info');
    $('#session-detail-modal').modal('hide');
}

function continueSession() {
    const sessionId = $('#detail-session-id').text();
    window.location.href = `/debug?session=${sessionId}`;
}
</script>
{% endblock %}
