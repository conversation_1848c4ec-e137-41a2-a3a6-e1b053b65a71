{% extends "base.html" %}

{% block title %}AI-GDB 代码知识库{% endblock %}

{% block content %}
<div class="row">
    <!-- 知识库统计 -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 知识库统计</h5>
            </div>
            <div class="card-body">
                <div class="row" id="knowledge-stats">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 代码搜索 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-search"></i> 代码搜索</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="search-query" class="form-label">搜索查询</label>
                    <input type="text" class="form-control" id="search-query" 
                           placeholder="输入函数名、变量名或代码片段...">
                </div>
                
                <div class="mb-3">
                    <label for="search-limit" class="form-label">结果数量</label>
                    <select class="form-select" id="search-limit">
                        <option value="5">5个结果</option>
                        <option value="10" selected>10个结果</option>
                        <option value="20">20个结果</option>
                        <option value="50">50个结果</option>
                    </select>
                </div>
                
                <button type="button" class="btn btn-primary" id="search-btn">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <button type="button" class="btn btn-secondary" id="clear-search-btn">
                    <i class="fas fa-times"></i> 清空
                </button>
            </div>
        </div>
        
        <!-- 知识库管理 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i> 知识库管理</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="project-path" class="form-label">项目路径</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="project-path" 
                               placeholder="输入项目根目录路径...">
                        <button class="btn btn-outline-secondary" type="button" id="browse-path-btn">
                            <i class="fas fa-folder-open"></i> 浏览
                        </button>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="build-knowledge-btn">
                        <i class="fas fa-hammer"></i> 构建知识库
                    </button>
                    <button type="button" class="btn btn-warning" id="rebuild-knowledge-btn">
                        <i class="fas fa-redo"></i> 重建知识库
                    </button>
                    <button type="button" class="btn btn-danger" id="clear-knowledge-btn">
                        <i class="fas fa-trash"></i> 清空知识库
                    </button>
                </div>
                
                <div class="mt-3">
                    <div class="progress" style="display: none;" id="build-progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%">
                            0%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 搜索结果 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> 搜索结果</h5>
                <span id="search-count" class="badge bg-secondary">0 个结果</span>
            </div>
            <div class="card-body">
                <div id="search-results">
                    <div class="text-center text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>输入搜索查询以查找相关代码</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 代码详情模态框 -->
<div class="modal fade" id="code-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-code"></i> 
                    <span id="modal-file-path">代码详情</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>文件路径:</strong> <span id="modal-full-path"></span><br>
                    <strong>行数范围:</strong> <span id="modal-line-range"></span><br>
                    <strong>相关符号:</strong> <span id="modal-symbols"></span>
                </div>
                <div class="border rounded">
                    <textarea id="modal-code-editor" style="height: 400px;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="open-in-debug-btn">
                    <i class="fas fa-external-link-alt"></i> 在调试器中打开
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 知识库构建日志模态框 -->
<div class="modal fade" id="build-log-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list-alt"></i> 构建日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="log-container" id="build-log-content" style="height: 400px;">
                    <!-- 构建日志内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let modalCodeEditor = null;
let buildLogger = null;

$(document).ready(function() {
    // 初始化组件
    initializeKnowledgeComponents();
    
    // 绑定事件处理器
    bindKnowledgeEventHandlers();
    
    // 加载知识库统计
    loadKnowledgeStats();
    
    // 监听WebSocket消息
    setupWebSocketHandlers();
});

function initializeKnowledgeComponents() {
    // 初始化模态框代码编辑器
    if (typeof CodeMirror !== 'undefined') {
        const modalTextarea = document.getElementById('modal-code-editor');
        if (modalTextarea) {
            modalCodeEditor = CodeMirror.fromTextArea(modalTextarea, {
                lineNumbers: true,
                mode: 'text/x-csrc',
                theme: 'monokai',
                readOnly: true,
                lineWrapping: true
            });
        }
    }
    
    // 初始化构建日志
    buildLogger = new AIGDB.components.Logger('build-log-content');
}

function bindKnowledgeEventHandlers() {
    // 搜索按钮
    $('#search-btn').click(function() {
        const query = $('#search-query').val().trim();
        if (!query) {
            AIGDB.utils.showNotification('请输入搜索查询', 'warning');
            return;
        }
        
        const limit = parseInt($('#search-limit').val());
        searchCode(query, limit);
    });
    
    // 回车搜索
    $('#search-query').keypress(function(e) {
        if (e.which === 13) {
            $('#search-btn').click();
        }
    });
    
    // 清空搜索
    $('#clear-search-btn').click(function() {
        $('#search-query').val('');
        $('#search-results').html(`
            <div class="text-center text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>输入搜索查询以查找相关代码</p>
            </div>
        `);
        $('#search-count').text('0 个结果');
    });
    
    // 知识库管理按钮
    $('#build-knowledge-btn').click(() => buildKnowledge(false));
    $('#rebuild-knowledge-btn').click(() => buildKnowledge(true));
    $('#clear-knowledge-btn').click(clearKnowledge);
    
    // 浏览路径按钮
    $('#browse-path-btn').click(function() {
        // 这里可以实现文件夹选择功能
        AIGDB.utils.showNotification('文件夹选择功能待实现', 'info');
    });
    
    // 在调试器中打开
    $('#open-in-debug-btn').click(function() {
        const filePath = $('#modal-full-path').text();
        if (filePath) {
            // 跳转到调试页面并加载文件
            window.location.href = `/debug?file=${encodeURIComponent(filePath)}`;
        }
    });
}

function loadKnowledgeStats() {
    AIGDB.utils.showLoading('#knowledge-stats', '加载统计信息...');
    
    $.get('/api/knowledge/stats')
        .done(function(response) {
            if (response.success) {
                renderKnowledgeStats(response.data);
            } else {
                AIGDB.utils.showError('#knowledge-stats', response.error);
            }
        })
        .fail(function() {
            AIGDB.utils.showError('#knowledge-stats', '加载统计信息失败');
        });
}

function renderKnowledgeStats(data) {
    const html = `
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-primary">${data.total_chunks || 0}</h3>
                <p class="mb-0">代码块</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-success">${data.total_symbols || 0}</h3>
                <p class="mb-0">符号</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-info">${data.file_count || 0}</h3>
                <p class="mb-0">文件</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="text-center">
                <h3 class="text-warning">${(data.languages || []).length}</h3>
                <p class="mb-0">语言</p>
            </div>
        </div>
        ${data.languages && data.languages.length > 0 ? `
        <div class="col-12 mt-3">
            <p><strong>支持的语言:</strong> ${data.languages.join(', ')}</p>
        </div>
        ` : ''}
    `;
    
    $('#knowledge-stats').html(html);
}

function searchCode(query, limit = 10) {
    AIGDB.utils.showLoading('#search-results', '搜索中...');
    
    if (window.wsManager) {
        wsManager.send('search_code', { query: query, top_k: limit });
    } else {
        // 备用API调用
        $.get('/api/knowledge/search', { query: query, limit: limit })
            .done(function(response) {
                if (response.success) {
                    renderSearchResults(response.data.results);
                    $('#search-count').text(`${response.data.results.length} 个结果`);
                } else {
                    AIGDB.utils.showError('#search-results', response.error);
                }
            })
            .fail(function() {
                AIGDB.utils.showError('#search-results', '搜索失败');
            });
    }
}

function renderSearchResults(results) {
    if (!results || results.length === 0) {
        $('#search-results').html(`
            <div class="text-center text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>未找到相关代码</p>
            </div>
        `);
        $('#search-count').text('0 个结果');
        return;
    }
    
    let html = '';
    results.forEach((result, index) => {
        const score = (result.score * 100).toFixed(1);
        const preview = result.content.substring(0, 200) + (result.content.length > 200 ? '...' : '');
        
        html += `
            <div class="card mb-3 search-result-item" data-index="${index}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        ${result.file_path}
                    </h6>
                    <span class="badge bg-primary">${score}%</span>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt"></i>
                        行 ${result.start_line}-${result.end_line}
                        ${result.symbols && result.symbols.length > 0 ? 
                          `<span class="ms-2"><i class="fas fa-tags"></i> ${result.symbols.join(', ')}</span>` : ''}
                    </p>
                    <pre class="bg-light p-2 rounded"><code>${preview}</code></pre>
                    <button type="button" class="btn btn-sm btn-outline-primary view-detail-btn" 
                            data-result='${JSON.stringify(result)}'>
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `;
    });
    
    $('#search-results').html(html);
    $('#search-count').text(`${results.length} 个结果`);
    
    // 绑定查看详情按钮
    $('.view-detail-btn').click(function() {
        const result = JSON.parse($(this).attr('data-result'));
        showCodeDetail(result);
    });
}

function showCodeDetail(result) {
    $('#modal-file-path').text(result.file_path.split('/').pop());
    $('#modal-full-path').text(result.file_path);
    $('#modal-line-range').text(`${result.start_line}-${result.end_line}`);
    $('#modal-symbols').text(result.symbols ? result.symbols.join(', ') : '无');
    
    if (modalCodeEditor) {
        modalCodeEditor.setValue(result.content);
    }
    
    $('#code-detail-modal').modal('show');
}

function buildKnowledge(rebuild = false) {
    const projectPath = $('#project-path').val().trim();
    if (!projectPath) {
        AIGDB.utils.showNotification('请输入项目路径', 'warning');
        return;
    }
    
    // 显示进度条
    $('#build-progress').show();
    $('.progress-bar').css('width', '0%').text('0%');
    
    // 禁用按钮
    $('#build-knowledge-btn, #rebuild-knowledge-btn').prop('disabled', true);
    
    // 清空日志
    buildLogger.clear();
    
    // 显示日志模态框
    $('#build-log-modal').modal('show');
    
    const action = rebuild ? 'rebuild' : 'build';
    buildLogger.log(`开始${rebuild ? '重建' : '构建'}知识库: ${projectPath}`, 'info');
    
    $.ajax({
        url: '/api/knowledge/build',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            project_path: projectPath,
            rebuild: rebuild
        })
    })
    .done(function(response) {
        if (response.success) {
            buildLogger.log('知识库构建完成', 'success');
            AIGDB.utils.showNotification('知识库构建完成', 'success');
            loadKnowledgeStats(); // 重新加载统计信息
        } else {
            buildLogger.log('知识库构建失败: ' + response.error, 'error');
            AIGDB.utils.showNotification('知识库构建失败', 'error');
        }
    })
    .fail(function() {
        buildLogger.log('知识库构建请求失败', 'error');
        AIGDB.utils.showNotification('知识库构建请求失败', 'error');
    })
    .always(function() {
        // 隐藏进度条
        $('#build-progress').hide();
        
        // 启用按钮
        $('#build-knowledge-btn, #rebuild-knowledge-btn').prop('disabled', false);
    });
}

function clearKnowledge() {
    if (!confirm('确定要清空知识库吗？此操作不可撤销。')) {
        return;
    }
    
    $.post('/api/knowledge/clear')
        .done(function(response) {
            if (response.success) {
                AIGDB.utils.showNotification('知识库已清空', 'success');
                loadKnowledgeStats();
                $('#clear-search-btn').click(); // 清空搜索结果
            } else {
                AIGDB.utils.showNotification('清空知识库失败: ' + response.error, 'error');
            }
        })
        .fail(function() {
            AIGDB.utils.showNotification('清空知识库请求失败', 'error');
        });
}

function setupWebSocketHandlers() {
    if (window.wsManager) {
        // 监听搜索结果
        wsManager.on('search_results', function(data) {
            if (data.success) {
                renderSearchResults(data.data.results);
                $('#search-count').text(`${data.data.results.length} 个结果`);
            } else {
                AIGDB.utils.showError('#search-results', data.error);
            }
        });
        
        // 监听构建进度
        wsManager.on('build_progress', function(data) {
            const progress = data.progress || 0;
            $('.progress-bar').css('width', progress + '%').text(progress + '%');
            
            if (data.message) {
                buildLogger.log(data.message, data.level || 'info');
            }
        });
    }
}
</script>
{% endblock %}
