"""
API路由定义
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from loguru import logger

try:
    from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
    from fastapi.responses import JSONResponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False


# 请求模型
class DebugRequest(BaseModel):
    fault_description: str
    executable_path: Optional[str] = None
    test_script_path: Optional[str] = None


class BreakpointRequest(BaseModel):
    location: str
    condition: Optional[str] = None


class ExpressionRequest(BaseModel):
    expression: str


class SearchRequest(BaseModel):
    query: str
    top_k: int = 5


class KnowledgeBuildRequest(BaseModel):
    project_path: Optional[str] = None
    rebuild: bool = False


# 响应模型
class APIResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    message: Optional[str] = None


def create_api_routes(ai_agent) -> APIRouter:
    """创建API路由"""
    if not FASTAPI_AVAILABLE:
        raise RuntimeError("FastAPI不可用")
    
    router = APIRouter()
    
    @router.get("/status")
    async def get_status():
        """获取系统状态"""
        try:
            # 获取调试状态
            debug_state = None
            if ai_agent.gdb_controller:
                debug_state = await ai_agent.gdb_controller.get_debug_state()
            
            # 获取知识库统计
            kb_stats = ai_agent.knowledge_base.get_statistics()
            
            # 获取当前会话信息
            session_info = None
            if ai_agent.current_session:
                session_info = ai_agent.memory_manager.get_session_summary()
            
            return APIResponse(
                success=True,
                data={
                    "is_debugging": ai_agent.is_debugging,
                    "current_round": ai_agent.current_round,
                    "max_rounds": ai_agent.max_debug_rounds,
                    "debug_state": debug_state.__dict__ if debug_state else None,
                    "knowledge_base_stats": kb_stats,
                    "current_session": session_info
                }
            )
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/debug/start")
    async def start_debug(request: DebugRequest, background_tasks: BackgroundTasks):
        """开始调试会话"""
        try:
            if ai_agent.is_debugging:
                return APIResponse(
                    success=False,
                    error="调试会话已在进行中"
                )
            
            # 在后台启动调试会话
            background_tasks.add_task(
                ai_agent.start_debug_session,
                request.fault_description
            )
            
            return APIResponse(
                success=True,
                message="调试会话已启动",
                data={"fault_description": request.fault_description}
            )
            
        except Exception as e:
            logger.error(f"启动调试失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/debug/stop")
    async def stop_debug():
        """停止调试会话"""
        try:
            if not ai_agent.is_debugging:
                return APIResponse(
                    success=False,
                    error="没有正在进行的调试会话"
                )
            
            ai_agent.is_debugging = False
            await ai_agent.gdb_controller.stop_debug_session()
            
            return APIResponse(
                success=True,
                message="调试会话已停止"
            )
            
        except Exception as e:
            logger.error(f"停止调试失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/debug/state")
    async def get_debug_state():
        """获取调试状态"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            debug_state = await ai_agent.gdb_controller.get_debug_state()
            
            return APIResponse(
                success=True,
                data=debug_state.__dict__ if debug_state else None
            )
            
        except Exception as e:
            logger.error(f"获取调试状态失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/breakpoint/set")
    async def set_breakpoint(request: BreakpointRequest):
        """设置断点"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            bp_id = await ai_agent.gdb_controller.set_breakpoint(
                request.location,
                request.condition
            )
            
            if bp_id:
                return APIResponse(
                    success=True,
                    data={"breakpoint_id": bp_id, "location": request.location},
                    message="断点设置成功"
                )
            else:
                return APIResponse(
                    success=False,
                    error="断点设置失败"
                )
                
        except Exception as e:
            logger.error(f"设置断点失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.delete("/breakpoint/{bp_id}")
    async def remove_breakpoint(bp_id: int):
        """删除断点"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            success = await ai_agent.gdb_controller.remove_breakpoint(bp_id)
            
            return APIResponse(
                success=success,
                message="断点删除成功" if success else "断点删除失败"
            )
            
        except Exception as e:
            logger.error(f"删除断点失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/breakpoints")
    async def get_breakpoints():
        """获取所有断点"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            breakpoints = ai_agent.gdb_controller.get_breakpoints()
            
            return APIResponse(
                success=True,
                data=[bp.__dict__ for bp in breakpoints]
            )
            
        except Exception as e:
            logger.error(f"获取断点失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/debug/continue")
    async def continue_execution():
        """继续执行"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            success = await ai_agent.gdb_controller.continue_execution()
            
            return APIResponse(
                success=success,
                message="继续执行" if success else "继续执行失败"
            )
            
        except Exception as e:
            logger.error(f"继续执行失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/debug/step-over")
    async def step_over():
        """单步执行（跳过函数）"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            success = await ai_agent.gdb_controller.step_over()
            
            return APIResponse(
                success=success,
                message="单步执行完成" if success else "单步执行失败"
            )
            
        except Exception as e:
            logger.error(f"单步执行失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/debug/step-into")
    async def step_into():
        """单步执行（进入函数）"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            success = await ai_agent.gdb_controller.step_into()
            
            return APIResponse(
                success=success,
                message="单步进入完成" if success else "单步进入失败"
            )
            
        except Exception as e:
            logger.error(f"单步进入失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/variables")
    async def get_variables(scope: str = "local"):
        """获取变量"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            variables = await ai_agent.gdb_controller.get_variables(scope)
            
            return APIResponse(
                success=True,
                data={
                    "scope": scope,
                    "variables": {name: var.__dict__ for name, var in variables.items()}
                }
            )
            
        except Exception as e:
            logger.error(f"获取变量失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/evaluate")
    async def evaluate_expression(request: ExpressionRequest):
        """计算表达式"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            result = await ai_agent.gdb_controller.evaluate_expression(request.expression)
            
            return APIResponse(
                success=True,
                data={
                    "expression": request.expression,
                    "result": result
                }
            )
            
        except Exception as e:
            logger.error(f"计算表达式失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/backtrace")
    async def get_backtrace():
        """获取调用栈"""
        try:
            if not ai_agent.gdb_controller:
                return APIResponse(
                    success=False,
                    error="GDB控制器未初始化"
                )
            
            frames = await ai_agent.gdb_controller.get_backtrace()
            
            return APIResponse(
                success=True,
                data=[frame.__dict__ for frame in frames]
            )
            
        except Exception as e:
            logger.error(f"获取调用栈失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/search")
    async def search_code(request: SearchRequest):
        """搜索代码"""
        try:
            results = ai_agent.knowledge_base.search_code(request.query, request.top_k)
            
            return APIResponse(
                success=True,
                data={
                    "query": request.query,
                    "results": [
                        {
                            "score": result.score,
                            "file_path": result.chunk.file_path,
                            "start_line": result.chunk.start_line,
                            "end_line": result.chunk.end_line,
                            "content": result.chunk.content,
                            "symbols": [s.name for s in result.chunk.symbols]
                        }
                        for result in results
                    ]
                }
            )
            
        except Exception as e:
            logger.error(f"搜索代码失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/knowledge/stats")
    async def get_knowledge_stats():
        """获取知识库统计"""
        try:
            stats = ai_agent.knowledge_base.get_statistics()
            
            return APIResponse(
                success=True,
                data=stats
            )
            
        except Exception as e:
            logger.error(f"获取知识库统计失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.post("/knowledge/build")
    async def build_knowledge_base(request: KnowledgeBuildRequest, background_tasks: BackgroundTasks):
        """构建知识库"""
        try:
            # 如果提供了项目路径，更新配置
            if request.project_path:
                ai_agent.config.project_code_dir = request.project_path
                logger.info(f"更新项目路径为: {request.project_path}")

            # 在后台启动知识库构建
            background_tasks.add_task(ai_agent.knowledge_base.build_knowledge_base)

            action = "重建" if request.rebuild else "构建"
            return APIResponse(
                success=True,
                message=f"知识库{action}已启动",
                data={
                    "project_path": ai_agent.config.project_code_dir,
                    "rebuild": request.rebuild
                }
            )

        except Exception as e:
            logger.error(f"启动知识库构建失败: {e}")
            return APIResponse(success=False, error=str(e))

    @router.post("/knowledge/rebuild")
    async def rebuild_knowledge_base(background_tasks: BackgroundTasks):
        """重建知识库"""
        try:
            background_tasks.add_task(ai_agent.knowledge_base.build_knowledge_base)

            return APIResponse(
                success=True,
                message="知识库重建已启动"
            )

        except Exception as e:
            logger.error(f"重建知识库失败: {e}")
            return APIResponse(success=False, error=str(e))

    @router.get("/knowledge/search")
    async def search_code_get(
        query: str = Query(..., description="搜索查询"),
        limit: int = Query(10, description="结果数量限制")
    ):
        """搜索代码 (GET方法)"""
        try:
            if not query.strip():
                return APIResponse(
                    success=False,
                    error="搜索查询不能为空"
                )

            results = ai_agent.knowledge_base.search_code(query, limit)

            return APIResponse(
                success=True,
                data={
                    "query": query,
                    "results": [
                        {
                            "score": result.score,
                            "file_path": result.chunk.file_path,
                            "start_line": result.chunk.start_line,
                            "end_line": result.chunk.end_line,
                            "content": result.chunk.content,
                            "symbols": [s.name for s in result.chunk.symbols]
                        }
                        for result in results
                    ]
                }
            )

        except Exception as e:
            logger.error(f"搜索代码失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/memory/sessions")
    async def get_debug_sessions():
        """获取调试会话历史"""
        try:
            sessions = []
            for session_id, session in ai_agent.memory_manager.sessions.items():
                summary = ai_agent.memory_manager.get_session_summary(session_id)
                sessions.append(summary)

            # 按时间排序
            sessions.sort(key=lambda x: x.get("duration", 0), reverse=True)

            return APIResponse(
                success=True,
                data=sessions
            )

        except Exception as e:
            logger.error(f"获取调试会话失败: {e}")
            return APIResponse(success=False, error=str(e))

    @router.get("/memory/sessions/{session_id}")
    async def get_session_detail(session_id: str):
        """获取调试会话详情"""
        try:
            # 检查会话是否存在
            if session_id not in ai_agent.memory_manager.sessions:
                raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")

            session = ai_agent.memory_manager.sessions[session_id]

            # 获取会话详细信息
            session_detail = {
                "session_id": session_id,
                "fault_description": session.error_description,  # 使用正确的属性名
                "start_time": session.start_time,
                "end_time": session.end_time,
                "status": session.status,
                "summary": session.summary,
                "memories": []
            }

            # 获取会话相关的记忆
            for memory in session.memories:
                session_detail["memories"].append({
                    "id": memory.id,
                    "timestamp": memory.timestamp,
                    "type": memory.type,
                    "content": memory.content,
                    "metadata": memory.metadata,
                    "importance": memory.importance
                })

            # 按时间排序记忆
            session_detail["memories"].sort(key=lambda x: x["timestamp"])

            return APIResponse(
                success=True,
                data=session_detail
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取会话详情失败: {e}")
            return APIResponse(success=False, error=str(e))
    
    @router.get("/memory/patterns")
    async def get_historical_patterns():
        """获取历史模式分析"""
        try:
            patterns = ai_agent.memory_manager.get_historical_patterns()

            return APIResponse(
                success=True,
                data=patterns
            )

        except Exception as e:
            logger.error(f"获取历史模式失败: {e}")
            return APIResponse(success=False, error=str(e))

    @router.get("/files")
    async def list_files(path: str = Query("", description="目录路径")):
        """列出文件和目录"""
        try:
            # 获取项目根目录（使用配置中的PROJECT_CODE_DIR）
            project_root = Path(ai_agent.config.project_code_dir)

            # 处理路径
            if path:
                target_path = project_root / path
            else:
                target_path = project_root

            # 安全检查：确保路径在项目根目录内
            try:
                target_path = target_path.resolve()
                project_root = project_root.resolve()
                target_path.relative_to(project_root)
            except ValueError:
                return APIResponse(
                    success=False,
                    error="路径不在项目范围内"
                )

            if not target_path.exists():
                return APIResponse(
                    success=False,
                    error="路径不存在"
                )

            if not target_path.is_dir():
                return APIResponse(
                    success=False,
                    error="路径不是目录"
                )

            # 列出文件和目录
            items = []
            try:
                for item in target_path.iterdir():
                    # 跳过隐藏文件和特殊目录
                    if item.name.startswith('.'):
                        continue

                    item_info = {
                        "name": item.name,
                        "path": str(item.relative_to(project_root)),
                        "type": "directory" if item.is_dir() else "file",
                        "size": item.stat().st_size if item.is_file() else 0
                    }

                    # 只显示代码文件
                    if item.is_file():
                        ext = item.suffix.lower()
                        if ext in ['.c', '.cpp', '.cc', '.cxx', '.h', '.hpp', '.py', '.js', '.html', '.css', '.md', '.txt']:
                            items.append(item_info)
                    else:
                        items.append(item_info)

            except PermissionError:
                return APIResponse(
                    success=False,
                    error="没有权限访问该目录"
                )

            # 排序：目录在前，文件在后
            items.sort(key=lambda x: (x["type"] == "file", x["name"].lower()))

            return APIResponse(
                success=True,
                data=items
            )

        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return APIResponse(success=False, error=str(e))

    @router.get("/files/content")
    async def get_file_content(path: str = Query(..., description="文件路径")):
        """获取文件内容"""
        try:
            # 获取项目根目录（使用配置中的PROJECT_CODE_DIR）
            project_root = Path(ai_agent.config.project_code_dir)
            target_path = project_root / path

            # 安全检查：确保路径在项目根目录内
            try:
                target_path = target_path.resolve()
                project_root = project_root.resolve()
                target_path.relative_to(project_root)
            except ValueError:
                return APIResponse(
                    success=False,
                    error="路径不在项目范围内"
                )

            if not target_path.exists():
                return APIResponse(
                    success=False,
                    error="文件不存在"
                )

            if not target_path.is_file():
                return APIResponse(
                    success=False,
                    error="路径不是文件"
                )

            # 检查文件大小（限制为1MB）
            if target_path.stat().st_size > 1024 * 1024:
                return APIResponse(
                    success=False,
                    error="文件过大（超过1MB）"
                )

            # 读取文件内容
            try:
                with open(target_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(target_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    return APIResponse(
                        success=False,
                        error="无法解码文件内容"
                    )

            return APIResponse(
                success=True,
                data={
                    "path": path,
                    "content": content,
                    "size": len(content),
                    "lines": content.count('\n') + 1
                }
            )

        except Exception as e:
            logger.error(f"获取文件内容失败: {e}")
            return APIResponse(success=False, error=str(e))

    return router
