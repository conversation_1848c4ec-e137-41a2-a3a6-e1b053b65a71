/**
 * AI-GDB Web界面主应用脚本
 * 提供通用功能和工具函数
 */

// 全局应用对象
window.AIGDB = {
    // 应用配置
    config: {
        apiBaseUrl: '/api',
        wsReconnectDelay: 1000,
        maxLogEntries: 1000,
        autoRefreshInterval: 5000
    },
    
    // 应用状态
    state: {
        isDebugging: false,
        currentSession: null,
        selectedFile: null,
        breakpoints: [],
        variables: []
    },
    
    // 工具函数
    utils: {},
    
    // 组件
    components: {},
    
    // 事件处理器
    handlers: {}
};

/**
 * 工具函数
 */
AIGDB.utils = {
    /**
     * 格式化时间戳
     */
    formatTimestamp: function(timestamp) {
        if (!timestamp || isNaN(timestamp)) {
            return new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) {
            return new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 显示通知
     */
    showNotification: function(message, type = 'info', duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }
        
        return notification;
    },
    
    /**
     * 显示加载状态
     */
    showLoading: function(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.innerHTML = `
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    ${text}
                </div>
            `;
        }
    },
    
    /**
     * 显示错误信息
     */
    showError: function(element, message) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }
    },
    
    /**
     * API请求封装
     */
    apiRequest: async function(endpoint, options = {}) {
        const url = AIGDB.config.apiBaseUrl + endpoint;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error(`API请求失败 (${endpoint}):`, error);
            throw error;
        }
    }
};

/**
 * 日志组件
 */
AIGDB.components.Logger = class {
    constructor(containerId, maxEntries = 1000) {
        this.container = document.getElementById(containerId);
        this.maxEntries = maxEntries;
        this.entries = [];
        
        if (!this.container) {
            console.warn(`日志容器 ${containerId} 不存在`);
        }
    }
    
    log(message, level = 'info') {
        // 处理undefined或null消息
        if (message === undefined || message === null) {
            message = '[空消息]';
        } else if (typeof message !== 'string') {
            message = String(message);
        }

        const entry = {
            timestamp: Date.now(),
            message: message,
            level: level || 'info'
        };

        this.entries.push(entry);

        // 限制条目数量
        if (this.entries.length > this.maxEntries) {
            this.entries.shift();
        }

        this.render();
        this.scrollToBottom();
    }
    
    clear() {
        this.entries = [];
        this.render();
    }
    
    render() {
        if (!this.container) return;

        const html = this.entries.map(entry => {
            const time = AIGDB.utils.formatTimestamp(entry.timestamp);
            const message = entry.message || '[空消息]';
            const level = entry.level || 'info';

            return `
                <div class="log-entry ${level}">
                    <span class="text-muted">[${time}]</span>
                    <span class="ms-2">${this.escapeHtml(message)}</span>
                </div>
            `;
        }).join('');

        this.container.innerHTML = html;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    scrollToBottom() {
        if (this.container) {
            this.container.scrollTop = this.container.scrollHeight;
        }
    }
};

/**
 * 文件浏览器组件
 */
AIGDB.components.FileBrowser = class {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentPath = '';
        this.files = [];
        this.selectedFile = null;
        this.onFileSelect = null;
        
        if (!this.container) {
            console.warn(`文件浏览器容器 ${containerId} 不存在`);
        }
    }
    
    async loadFiles(path = '') {
        try {
            AIGDB.utils.showLoading(this.container, '加载文件列表...');
            
            const response = await AIGDB.utils.apiRequest(`/files?path=${encodeURIComponent(path)}`);
            
            if (response.success) {
                this.currentPath = path;
                this.files = response.data;
                this.render();
            } else {
                AIGDB.utils.showError(this.container, response.error);
            }
        } catch (error) {
            AIGDB.utils.showError(this.container, `加载文件失败: ${error.message}`);
        }
    }
    
    render() {
        if (!this.container) return;
        
        let html = '';
        
        // 返回上级目录按钮
        if (this.currentPath) {
            html += `
                <div class="file-item" data-type="parent" data-path="">
                    <i class="fas fa-arrow-left"></i>
                    <span>返回上级</span>
                </div>
            `;
        }
        
        // 文件和目录列表
        this.files.forEach(file => {
            const icon = file.type === 'directory' ? 'fa-folder' : 'fa-file';
            const selected = this.selectedFile === file.path ? 'selected' : '';
            
            html += `
                <div class="file-item ${selected}" data-type="${file.type}" data-path="${file.path}">
                    <i class="fas ${icon}"></i>
                    <span>${file.name}</span>
                </div>
            `;
        });
        
        this.container.innerHTML = html;
        
        // 绑定点击事件
        this.container.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', () => {
                const type = item.dataset.type;
                const path = item.dataset.path;
                
                if (type === 'parent') {
                    const parentPath = this.currentPath.split('/').slice(0, -1).join('/');
                    this.loadFiles(parentPath);
                } else if (type === 'directory') {
                    this.loadFiles(path);
                } else if (type === 'file') {
                    this.selectFile(path);
                }
            });
        });
    }
    
    selectFile(path) {
        this.selectedFile = path;
        AIGDB.state.selectedFile = path;
        
        // 更新选中状态
        this.container.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        const selectedItem = this.container.querySelector(`[data-path="${path}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }
        
        // 触发回调
        if (this.onFileSelect) {
            this.onFileSelect(path);
        }
    }
};

/**
 * 初始化应用
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('AI-GDB Web界面已加载');
    
    // 初始化WebSocket事件监听
    if (window.wsManager) {
        // 监听调试状态更新
        wsManager.on('debug_status', function(data) {
            AIGDB.state.isDebugging = data.is_debugging;
            AIGDB.state.currentSession = data.session;
            
            // 触发状态更新事件
            document.dispatchEvent(new CustomEvent('debug_status_changed', {
                detail: data
            }));
        });
        
        // 监听日志消息
        wsManager.on('log', function(data) {
            if (window.logger) {
                window.logger.log(data.message, data.level);
            }
        });
    }
    
    // 初始化工具提示
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    console.log('AI-GDB应用初始化完成');
});
