/**
 * WebSocket 连接管理器
 * 处理与后端的实时通信
 */

class WebSocketManager {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.isConnecting = false;
        this.messageHandlers = new Map();
        this.statusIndicator = document.getElementById('status-indicator');
        
        // 绑定事件处理器
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
        
        // 自动连接
        this.connect();
    }
    
    /**
     * 建立WebSocket连接
     */
    connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }
        
        this.isConnecting = true;
        this.updateStatus('connecting', '连接中...');
        
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            this.ws = new WebSocket(wsUrl);
            this.ws.onopen = this.onOpen;
            this.ws.onmessage = this.onMessage;
            this.ws.onclose = this.onClose;
            this.ws.onerror = this.onError;
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.isConnecting = false;
            this.updateStatus('error', '连接失败');
            this.scheduleReconnect();
        }
    }
    
    /**
     * 连接成功处理
     */
    onOpen(event) {
        console.log('WebSocket连接已建立');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.updateStatus('connected', '已连接');
        
        // 触发连接成功事件
        this.emit('connected', event);
    }
    
    /**
     * 消息接收处理
     */
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('收到WebSocket消息:', data);
            
            // 根据消息类型分发处理
            if (data.type && this.messageHandlers.has(data.type)) {
                const handlers = this.messageHandlers.get(data.type);
                handlers.forEach(handler => {
                    try {
                        handler(data);
                    } catch (error) {
                        console.error(`消息处理器错误 (${data.type}):`, error);
                    }
                });
            }
            
            // 触发通用消息事件
            this.emit('message', data);
            
        } catch (error) {
            console.error('WebSocket消息解析失败:', error);
        }
    }
    
    /**
     * 连接关闭处理
     */
    onClose(event) {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        this.isConnecting = false;
        this.updateStatus('disconnected', '连接断开');
        
        // 触发断开连接事件
        this.emit('disconnected', event);
        
        // 如果不是正常关闭，尝试重连
        if (event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 连接错误处理
     */
    onError(event) {
        console.error('WebSocket连接错误:', event);
        this.isConnecting = false;
        this.updateStatus('error', '连接错误');
        
        // 触发错误事件
        this.emit('error', event);
    }
    
    /**
     * 发送消息
     */
    send(type, data = {}) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.warn('WebSocket未连接，无法发送消息');
            return false;
        }

        try {
            const message = {
                type: type,
                id: this.generateMessageId(),
                data: data,
                timestamp: Date.now()
            };

            this.ws.send(JSON.stringify(message));
            console.log('发送WebSocket消息:', message);
            return true;

        } catch (error) {
            console.error('发送WebSocket消息失败:', error);
            return false;
        }
    }

    /**
     * 生成消息ID
     */
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 注册消息处理器
     */
    on(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }
    
    /**
     * 移除消息处理器
     */
    off(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(type, data) {
        const event = new CustomEvent(`ws_${type}`, { detail: data });
        document.dispatchEvent(event);
    }
    
    /**
     * 更新状态指示器
     */
    updateStatus(status, text) {
        if (!this.statusIndicator) return;
        
        // 移除所有状态类
        this.statusIndicator.classList.remove('connected', 'disconnected', 'error');
        
        // 添加新状态类
        this.statusIndicator.classList.add(status);
        
        // 更新文本
        const icon = this.statusIndicator.querySelector('i');
        const textNode = this.statusIndicator.childNodes[this.statusIndicator.childNodes.length - 1];
        if (textNode && textNode.nodeType === Node.TEXT_NODE) {
            textNode.textContent = ` ${text}`;
        }
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            this.updateStatus('error', '连接失败');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
        
        console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
        this.updateStatus('disconnected', `重连中(${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }
    
    /**
     * 手动重连
     */
    reconnect() {
        this.reconnectAttempts = 0;
        this.connect();
    }
    
    /**
     * 关闭连接
     */
    close() {
        if (this.ws) {
            this.ws.close(1000, '用户主动关闭');
        }
    }
    
    /**
     * 获取连接状态
     */
    getState() {
        if (!this.ws) return 'CLOSED';
        
        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'CONNECTING';
            case WebSocket.OPEN:
                return 'OPEN';
            case WebSocket.CLOSING:
                return 'CLOSING';
            case WebSocket.CLOSED:
                return 'CLOSED';
            default:
                return 'UNKNOWN';
        }
    }
}

// 全局WebSocket管理器实例
let wsManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    wsManager = new WebSocketManager();
    
    // 将实例暴露到全局作用域
    window.wsManager = wsManager;
});

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
    if (wsManager) {
        wsManager.close();
    }
});
