/* AI-GDB 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    color: #28a745;
}

/* 状态指示器 */
#status-indicator {
    font-size: 0.9rem;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
}

#status-indicator.connected {
    background-color: #28a745 !important;
}

#status-indicator.disconnected {
    background-color: #6c757d !important;
}

#status-indicator.error {
    background-color: #dc3545 !important;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
    transform: translateY(-1px);
}

/* 徽章样式 */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* 加载动画 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 代码编辑器样式 */
.CodeMirror {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.CodeMirror-focused {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 调试输出样式 */
.debug-output {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    padding: 1rem;
    border-radius: 0.375rem;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.debug-output .gdb-prompt {
    color: #4ec9b0;
    font-weight: bold;
}

.debug-output .gdb-output {
    color: #d4d4d4;
}

.debug-output .gdb-error {
    color: #f44747;
}

.debug-output .gdb-warning {
    color: #ffcc02;
}

/* 文件树样式 */
.file-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
}

.file-tree .file-item {
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out;
}

.file-tree .file-item:hover {
    background-color: #e9ecef;
}

.file-tree .file-item.selected {
    background-color: #007bff;
    color: white;
}

.file-tree .file-item i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

/* 变量监控样式 */
.variable-monitor {
    max-height: 300px;
    overflow-y: auto;
}

.variable-item {
    padding: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
}

.variable-item:last-child {
    border-bottom: none;
}

.variable-name {
    font-weight: bold;
    color: #007bff;
}

.variable-type {
    color: #6c757d;
    font-style: italic;
}

.variable-value {
    color: #28a745;
}

/* 断点列表样式 */
.breakpoint-list {
    max-height: 200px;
    overflow-y: auto;
}

.breakpoint-item {
    padding: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breakpoint-item:last-child {
    border-bottom: none;
}

.breakpoint-location {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
}

/* 日志样式 */
.log-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
}

.log-entry {
    padding: 0.25rem 0;
    border-bottom: 1px solid #e9ecef;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry.info {
    color: #007bff;
}

.log-entry.warning {
    color: #ffc107;
}

.log-entry.error {
    color: #dc3545;
}

.log-entry.success {
    color: #28a745;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .debug-output {
        font-size: 12px;
        max-height: 250px;
    }
    
    .CodeMirror {
        font-size: 12px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    transition: width 0.3s ease-in-out;
}

/* 表格样式 */
.table {
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
