"""
WebSocket处理器
"""

import json
import asyncio
from typing import Dict, List, Set
from loguru import logger

try:
    from fastapi import WebSocket, WebSocketDisconnect
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False


class WebSocketHandler:
    """WebSocket处理器"""
    
    def __init__(self, ai_agent):
        self.ai_agent = ai_agent
        self.active_connections: Set[WebSocket] = set()
        self.message_handlers: Dict[str, callable] = {}

        # 注册消息处理器
        self._register_handlers()

        # 设置AI Agent的事件广播器
        self.ai_agent.set_event_broadcaster(self._broadcast_to_all_clients)

    async def _broadcast_to_all_clients(self, message: Dict):
        """向所有连接的客户端广播消息"""
        if not self.active_connections:
            return

        # 创建要发送的消息
        broadcast_message = {
            "type": message.get("type", "debug_output"),
            "data": message,
            "timestamp": asyncio.get_event_loop().time()
        }

        # 向所有活跃连接发送消息
        disconnected_clients = set()
        for websocket in self.active_connections:
            success = await self._send_message(websocket, broadcast_message)
            if not success:
                disconnected_clients.add(websocket)

        # 移除断开的连接
        for websocket in disconnected_clients:
            self.active_connections.discard(websocket)
    
    def _register_handlers(self):
        """注册消息处理器"""
        self.message_handlers = {
            "ping": self._handle_ping,
            "get_status": self._handle_get_status,
            "start_debug": self._handle_start_debug,
            "stop_debug": self._handle_stop_debug,
            "set_breakpoint": self._handle_set_breakpoint,
            "remove_breakpoint": self._handle_remove_breakpoint,
            "continue_execution": self._handle_continue_execution,
            "step_over": self._handle_step_over,
            "step_into": self._handle_step_into,
            "get_variables": self._handle_get_variables,
            "evaluate_expression": self._handle_evaluate_expression,
            "search_code": self._handle_search_code,
        }
    
    async def websocket_endpoint(self, websocket: WebSocket):
        """WebSocket端点"""
        if not FASTAPI_AVAILABLE:
            await websocket.close(code=1000, reason="FastAPI不可用")
            return

        await websocket.accept()
        self.active_connections.add(websocket)

        logger.info(f"WebSocket连接建立: {websocket.client}")

        status_task = None
        try:
            # 发送欢迎消息
            await self._send_message(websocket, {
                "type": "welcome",
                "message": "连接到AI-GDB调试工具",
                "timestamp": asyncio.get_event_loop().time()
            })

            # 启动状态广播任务
            status_task = asyncio.create_task(self._status_broadcast_loop(websocket))

            # 消息处理循环
            while websocket in self.active_connections:
                try:
                    # 检查连接状态
                    if websocket.client_state.name != "CONNECTED":
                        logger.info(f"WebSocket连接状态异常: {websocket.client_state.name}")
                        break

                    # 接收消息
                    data = await websocket.receive_text()
                    message = json.loads(data)

                    # 处理消息
                    await self._handle_message(websocket, message)

                except json.JSONDecodeError:
                    if websocket in self.active_connections:
                        await self._send_error(websocket, "无效的JSON格式")
                except WebSocketDisconnect:
                    logger.info(f"WebSocket连接在消息处理中断开: {websocket.client}")
                    break
                except Exception as e:
                    logger.error(f"处理WebSocket消息失败: {e}")
                    if websocket in self.active_connections:
                        await self._send_error(websocket, str(e))
                    break

        except WebSocketDisconnect:
            logger.info(f"WebSocket连接断开: {websocket.client}")
        except Exception as e:
            logger.error(f"WebSocket连接错误: {e}")
        finally:
            # 清理连接
            self.active_connections.discard(websocket)
            if status_task:
                status_task.cancel()
                try:
                    await status_task
                except asyncio.CancelledError:
                    pass
            logger.info(f"WebSocket连接清理完成: {websocket.client}")
    
    async def _handle_message(self, websocket: WebSocket, message: Dict):
        """处理消息"""
        message_type = message.get("type")
        message_id = message.get("id")

        if message_type in self.message_handlers:
            try:
                # 传递完整的消息给处理器，让处理器自己决定如何解析数据
                response = await self.message_handlers[message_type](message)

                # 发送响应，包含原始消息类型以便前端识别
                response_data = {
                    "type": message_type,  # 保持原始消息类型
                    "id": message_id,
                    **response  # 展开响应数据
                }

                await self._send_message(websocket, {
                    "type": "response",
                    "id": message_id,
                    "data": response_data
                })

            except Exception as e:
                logger.error(f"处理消息 {message_type} 失败: {e}")
                await self._send_error(websocket, str(e), message_id)
        else:
            await self._send_error(websocket, f"未知消息类型: {message_type}", message_id)
    
    async def _send_message(self, websocket: WebSocket, message: Dict):
        """发送消息"""
        try:
            # 检查连接是否仍然活跃
            if websocket not in self.active_connections:
                return False

            # 检查WebSocket状态
            if websocket.client_state.name != "CONNECTED":
                return False

            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            return True
        except Exception as e:
            # 连接断开时移除连接
            self.active_connections.discard(websocket)
            logger.debug(f"发送WebSocket消息失败，连接已移除: {e}")
            return False
    
    async def _send_error(self, websocket: WebSocket, error: str, message_id: str = None):
        """发送错误消息"""
        error_message = {
            "type": "error",
            "error": error,
            "timestamp": asyncio.get_event_loop().time()
        }

        if message_id:
            error_message["id"] = message_id

        return await self._send_message(websocket, error_message)
    
    async def _status_broadcast_loop(self, websocket: WebSocket):
        """状态广播循环"""
        try:
            while websocket in self.active_connections:
                # 检查连接状态
                if websocket.client_state.name != "CONNECTED":
                    break

                # 获取当前状态
                status = await self._get_current_status()

                # 广播状态
                success = await self._send_message(websocket, {
                    "type": "status_update",
                    "data": status,
                    "timestamp": asyncio.get_event_loop().time()
                })

                # 如果发送失败，退出循环
                if not success:
                    break

                # 等待一段时间
                await asyncio.sleep(2)  # 每2秒广播一次状态

        except asyncio.CancelledError:
            logger.debug(f"状态广播任务被取消: {websocket.client}")
        except Exception as e:
            logger.error(f"状态广播失败: {e}")
        finally:
            logger.debug(f"状态广播循环结束: {websocket.client}")
    
    async def _get_current_status(self) -> Dict:
        """获取当前状态"""
        try:
            # 获取调试状态
            debug_state = None
            if self.ai_agent.gdb_controller:
                debug_state = await self.ai_agent.gdb_controller.get_debug_state()
            
            # 获取会话信息
            session_info = None
            if self.ai_agent.current_session:
                session_info = self.ai_agent.memory_manager.get_session_summary()
            
            return {
                "is_debugging": self.ai_agent.is_debugging,
                "current_round": self.ai_agent.current_round,
                "max_rounds": self.ai_agent.max_debug_rounds,
                "debug_state": debug_state.__dict__ if debug_state else None,
                "current_session": session_info
            }
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {"error": str(e)}
    
    # 消息处理器实现
    async def _handle_ping(self, message: Dict) -> Dict:
        """处理ping消息"""
        return {"pong": True, "timestamp": asyncio.get_event_loop().time()}

    async def _handle_get_status(self, message: Dict) -> Dict:
        """处理获取状态消息"""
        return await self._get_current_status()
    
    async def _handle_start_debug(self, message: Dict) -> Dict:
        """处理开始调试消息"""
        logger.info(f"收到start_debug消息，完整内容: {message}")

        # 支持两种消息格式：
        # 1. 新格式: {"type": "start_debug", "data": {"fault_description": "..."}}
        # 2. 旧格式: {"type": "start_debug", "fault_description": "..."}
        fault_description = None

        if "data" in message and isinstance(message["data"], dict):
            # 新格式：数据在data字段中
            fault_description = message["data"].get("fault_description")
            logger.info(f"使用新格式，从data字段获取故障描述: {fault_description}")
        else:
            # 旧格式：数据直接在顶层
            fault_description = message.get("fault_description")
            logger.info(f"使用旧格式，从顶层获取故障描述: {fault_description}")

        if not fault_description:
            logger.warning(f"收到start_debug消息但缺少故障描述，消息内容: {message}")
            return {"success": False, "error": "缺少故障描述"}

        logger.info(f"开始处理调试请求，故障描述: {fault_description}")

        # 直接调用AI Agent的start_debug_session方法
        # 该方法已经处理了后台任务创建和状态检查
        return await self.ai_agent.start_debug_session(fault_description)
    
    async def _handle_stop_debug(self, message: Dict) -> Dict:
        """处理停止调试消息"""
        if not self.ai_agent.is_debugging:
            return {"success": False, "error": "没有正在进行的调试会话"}

        # 停止调试会话并重置状态
        self.ai_agent.is_debugging = False
        self.ai_agent.current_round = 0

        # 停止GDB会话
        await self.ai_agent.gdb_controller.stop_debug_session()

        # 结束当前会话
        if self.ai_agent.current_session:
            self.ai_agent.memory_manager.end_session(
                self.ai_agent.current_session.session_id,
                "stopped",
                "用户手动停止调试会话"
            )
            self.ai_agent.current_session = None

        logger.info("调试会话已手动停止")

        return {"success": True, "message": "调试会话已停止"}
    
    async def _handle_set_breakpoint(self, message: Dict) -> Dict:
        """处理设置断点消息"""
        # 支持新旧格式
        data = message.get("data", message)
        location = data.get("location")
        condition = data.get("condition")
        
        if not location:
            return {"success": False, "error": "缺少断点位置"}
        
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}
        
        bp_id = await self.ai_agent.gdb_controller.set_breakpoint(location, condition)
        
        if bp_id:
            return {
                "success": True,
                "data": {"breakpoint_id": bp_id, "location": location},
                "message": "断点设置成功"
            }
        else:
            return {"success": False, "error": "断点设置失败"}
    
    async def _handle_remove_breakpoint(self, message: Dict) -> Dict:
        """处理删除断点消息"""
        # 支持新旧格式
        data = message.get("data", message)
        bp_id = data.get("breakpoint_id")
        
        if bp_id is None:
            return {"success": False, "error": "缺少断点ID"}
        
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}
        
        success = await self.ai_agent.gdb_controller.remove_breakpoint(bp_id)
        
        return {
            "success": success,
            "message": "断点删除成功" if success else "断点删除失败"
        }
    
    async def _handle_continue_execution(self, message: Dict) -> Dict:
        """处理继续执行消息"""
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}

        success = await self.ai_agent.gdb_controller.continue_execution()

        return {
            "success": success,
            "message": "继续执行" if success else "继续执行失败"
        }

    async def _handle_step_over(self, message: Dict) -> Dict:
        """处理单步执行消息"""
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}

        success = await self.ai_agent.gdb_controller.step_over()

        return {
            "success": success,
            "message": "单步执行完成" if success else "单步执行失败"
        }

    async def _handle_step_into(self, message: Dict) -> Dict:
        """处理单步进入消息"""
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}

        success = await self.ai_agent.gdb_controller.step_into()

        return {
            "success": success,
            "message": "单步进入完成" if success else "单步进入失败"
        }
    
    async def _handle_get_variables(self, data: Dict) -> Dict:
        """处理获取变量消息"""
        scope = data.get("scope", "local")
        
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}
        
        variables = await self.ai_agent.gdb_controller.get_variables(scope)
        
        return {
            "success": True,
            "data": {
                "scope": scope,
                "variables": {name: var.__dict__ for name, var in variables.items()}
            }
        }
    
    async def _handle_evaluate_expression(self, data: Dict) -> Dict:
        """处理计算表达式消息"""
        expression = data.get("expression")
        
        if not expression:
            return {"success": False, "error": "缺少表达式"}
        
        if not self.ai_agent.gdb_controller:
            return {"success": False, "error": "GDB控制器未初始化"}
        
        result = await self.ai_agent.gdb_controller.evaluate_expression(expression)
        
        return {
            "success": True,
            "data": {
                "expression": expression,
                "result": result
            }
        }
    
    async def _handle_search_code(self, data: Dict) -> Dict:
        """处理搜索代码消息"""
        query = data.get("query")
        top_k = data.get("top_k", 5)
        
        if not query:
            return {"success": False, "error": "缺少搜索查询"}
        
        results = self.ai_agent.knowledge_base.search_code(query, top_k)
        
        return {
            "success": True,
            "data": {
                "query": query,
                "results": [
                    {
                        "score": result.score,
                        "file_path": result.chunk.file_path,
                        "start_line": result.chunk.start_line,
                        "end_line": result.chunk.end_line,
                        "content": result.chunk.content[:500],  # 限制内容长度
                        "symbols": [s.name for s in result.chunk.symbols]
                    }
                    for result in results
                ]
            }
        }
    
    async def broadcast_to_all(self, message: Dict):
        """向所有连接广播消息"""
        if not self.active_connections:
            return

        # 创建广播任务
        tasks = []
        disconnected_connections = []

        for websocket in self.active_connections.copy():
            # 检查连接状态
            if websocket.client_state.name != "CONNECTED":
                disconnected_connections.append(websocket)
                continue

            tasks.append(self._send_message(websocket, message))

        # 移除断开的连接
        for websocket in disconnected_connections:
            self.active_connections.discard(websocket)

        # 并发发送
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 检查发送结果，移除失败的连接
            failed_connections = []
            for i, result in enumerate(results):
                if result is False or isinstance(result, Exception):
                    # 找到对应的websocket并标记为失败
                    websocket_list = list(self.active_connections)
                    if i < len(websocket_list):
                        failed_connections.append(websocket_list[i])

            # 移除失败的连接
            for websocket in failed_connections:
                self.active_connections.discard(websocket)
